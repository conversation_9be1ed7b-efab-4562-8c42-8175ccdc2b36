#!/usr/bin/env python3
"""
Script để verify bot đang hoạt động và gửi test message
"""
import asyncio
import sys
import os
from telegram import Bo<PERSON>
from telegram.error import TelegramError

# Bot configuration from environment variables
BOT_TOKEN = os.getenv('TELEGRAM_BOT_TOKEN')
CHAT_ID = os.getenv('TELEGRAM_CHAT_ID')

async def verify_bot():
    """Verify bot và gửi test message"""
    if not BOT_TOKEN or not CHAT_ID:
        print("❌ Error: TELEGRAM_BOT_TOKEN and TELEGRAM_CHAT_ID environment variables are required")
        print("💡 Set them with:")
        print("   export TELEGRAM_BOT_TOKEN='your_bot_token'")
        print("   export TELEGRAM_CHAT_ID='your_chat_id'")
        print("💡 Or create a .env file (see .env.example)")
        return

    bot = Bot(token=BOT_TOKEN)
    chat_id = int(CHAT_ID)
    
    try:
        print("🤖 Verifying bot status...")
        me = await bot.get_me()
        print(f"   ✅ Bot @{me.username} is active")
        
        print(f"\n📤 Sending test message with instructions...")
        message = await bot.send_message(
            chat_id=chat_id,
            text="""🤖 **Bot Test Message**

Bot đang hoạt động! Để test các handlers:

🔹 **Gửi /start** - Test start command
🔹 **Gửi /test** - Test test command  
🔹 **Gửi /help** - Test help command
🔹 **Gửi bất kỳ text nào** - Test message handler

📊 **Monitor logs:**
```
docker logs telegram-bot --tail 20 -f
```

Sau khi gửi commands, check logs để xem debug messages!""",
            parse_mode='Markdown'
        )
        
        print(f"   ✅ Test message sent! Message ID: {message.message_id}")
        print(f"\n📋 Next steps:")
        print(f"   1. Open Telegram app")
        print(f"   2. Find bot @{me.username}")
        print(f"   3. Send /start command")
        print(f"   4. Monitor logs: docker logs telegram-bot --tail 20 -f")
        print(f"   5. Look for debug messages starting with '🔄 POLLING DEBUG'")
        
        return True
        
    except TelegramError as e:
        print(f"❌ Telegram error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Bot Verification")
    print("=" * 30)
    
    try:
        result = asyncio.run(verify_bot())
        sys.exit(0 if result else 1)
    except Exception as e:
        print(f"❌ Fatal error: {e}")
        sys.exit(1)
