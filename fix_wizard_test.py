#!/usr/bin/env python3
"""Fix and test wizard functionality"""

import sys
import os

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.infrastructure.telegram.handlers.main_handler import MainTelegramHandler
from src.infrastructure.telegram.handlers.wizard_handler import WizardHandler
from src.infrastructure.telegram.telegram_base import UserSessionManager

class MockUpdate:
    def __init__(self, user_id, text):
        self.effective_user = MockUser(user_id)
        self.message = MockMessage(text)

class MockUser:
    def __init__(self, user_id):
        self.id = user_id

class MockMessage:
    def __init__(self, text):
        self.text = text
        
    async def reply_text(self, text, **kwargs):
        print(f"📤 Bot reply: {text}")

async def test_wizard_fix():
    """Test wizard functionality with fix"""
    print("🧪 Testing wizard fix...")
    
    # Create handlers
    token = os.getenv('TELEGRAM_BOT_TOKEN')
    chat_id = os.getenv('TELEGRAM_CHAT_ID')

    if not token or not chat_id:
        print("❌ Error: TELEGRAM_BOT_TOKEN and TELEGRAM_CHAT_ID environment variables are required")
        print("💡 Set them with:")
        print("   export TELEGRAM_BOT_TOKEN='your_bot_token'")
        print("   export TELEGRAM_CHAT_ID='your_chat_id'")
        print("💡 Or create a .env file (see .env.example)")
        return

    chat_id = int(chat_id)
    
    main_handler = MainTelegramHandler(token, chat_id)
    wizard_handler = main_handler.wizard_handler
    
    user_id = 12345
    
    print(f"Session manager ID: {id(main_handler.session_manager)}")
    print(f"Wizard session manager ID: {id(wizard_handler.session_manager)}")
    print(f"Same instance: {main_handler.session_manager is wizard_handler.session_manager}")
    
    # Test 1: Start createbot wizard
    print("\n🔧 Test 1: Starting createbot wizard...")
    update = MockUpdate(user_id, "/createbot")
    await wizard_handler.handle_createbot_wizard(update, None)
    
    # Check state after wizard start
    is_in_wizard = main_handler._is_in_wizard(user_id)
    current_step = main_handler._get_wizard_step(user_id)
    print(f"After wizard start - in_wizard: {is_in_wizard}, step: {current_step}")
    
    # Test 2: Send symbol
    print("\n🔧 Test 2: Sending symbol 'eth'...")
    update = MockUpdate(user_id, "eth")
    
    # Check state before processing message
    is_in_wizard = main_handler._is_in_wizard(user_id)
    current_step = main_handler._get_wizard_step(user_id)
    print(f"Before message processing - in_wizard: {is_in_wizard}, step: {current_step}")
    
    # Process message
    await main_handler.handle_message(update, None)
    
    # Check state after processing message
    is_in_wizard = main_handler._is_in_wizard(user_id)
    current_step = main_handler._get_wizard_step(user_id)
    print(f"After message processing - in_wizard: {is_in_wizard}, step: {current_step}")
    
    # Test 3: Send amount
    print("\n🔧 Test 3: Sending amount '100'...")
    update = MockUpdate(user_id, "100")
    await main_handler.handle_message(update, None)
    
    # Check final state
    is_in_wizard = main_handler._is_in_wizard(user_id)
    current_step = main_handler._get_wizard_step(user_id)
    print(f"After amount processing - in_wizard: {is_in_wizard}, step: {current_step}")
    
    print("✅ Wizard test completed!")

if __name__ == "__main__":
    import asyncio
    asyncio.run(test_wizard_fix())
