#!/usr/bin/env python3
"""
Test script for Telegram /createbot command
Simulates the /createbot flow step by step
"""

import os
import sys
import json
import subprocess
import time
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

def print_step(step, message):
    print(f"🔵 Step {step}: {message}")

def print_success(message):
    print(f"✅ {message}")

def print_error(message):
    print(f"❌ {message}")

def print_info(message):
    print(f"ℹ️ {message}")

def run_command(cmd, cwd=None):
    """Run shell command and return result"""
    try:
        result = subprocess.run(
            cmd, 
            shell=True, 
            capture_output=True, 
            text=True, 
            cwd=cwd or os.getcwd()
        )
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        return False, "", str(e)

def test_createbot_flow():
    """Test the complete /createbot flow"""
    
    print("🧪 Testing Telegram /createbot Command Flow")
    print("=" * 50)
    
    # Step 1: Check prerequisites
    print_step(1, "Checking prerequisites")
    
    # Check if telegram bot is running
    success, stdout, stderr = run_command("docker ps --format '{{.Names}}' | grep telegram-bot")
    if success and "telegram-bot" in stdout:
        print_success("Telegram bot is running")
    else:
        print_error("Telegram bot is not running")
        print_info("Start with: ./bot.sh telegram")
        return False
    
    # Step 2: Test container naming
    print_step(2, "Testing container naming logic")
    
    # Import the shell constants to test naming
    try:
        # Test symbols that would be used in /createbot
        test_cases = [
            ("eth", "ethusdt"),
            ("btc", "btcusdt"),
            ("sol", "solusdt"),
            ("ETH/USDT:USDT", "ethusdt"),
            ("BTC/USDT:USDT", "btcusdt")
        ]
        
        for symbol, expected in test_cases:
            # Use the shell function to get container name
            success, stdout, stderr = run_command(
                f"source src/core/shell_constants.sh && get_container_name '{symbol}'"
            )
            if success:
                actual = stdout.strip()
                if actual == expected:
                    print_success(f"Symbol '{symbol}' -> Container '{actual}' ✓")
                else:
                    print_error(f"Symbol '{symbol}' -> Expected '{expected}', got '{actual}'")
            else:
                print_error(f"Failed to get container name for '{symbol}': {stderr}")
                
    except Exception as e:
        print_error(f"Container naming test failed: {e}")
    
    # Step 3: Test credential handling
    print_step(3, "Testing credential handling")
    
    # Check if credentials directory exists
    creds_dir = Path.home() / ".autotrader" / "credentials"
    if creds_dir.exists():
        print_success(f"Credentials directory exists: {creds_dir}")
        
        # List available profiles
        cred_files = list(creds_dir.glob("*.json"))
        if cred_files:
            print_info(f"Available credential profiles: {[f.stem for f in cred_files]}")
        else:
            print_info("No credential profiles found")
    else:
        print_info(f"Credentials directory will be created: {creds_dir}")
    
    # Step 4: Test Docker mounts
    print_step(4, "Testing Docker mount paths")
    
    mount_tests = [
        ("configs", "/app/configs"),
        ("data", "/app/data"), 
        ("logs", "/app/logs")
    ]
    
    for local_dir, container_path in mount_tests:
        local_path = Path.cwd() / local_dir
        abs_path = local_path.resolve()
        
        success, stdout, stderr = run_command(
            f"docker run --rm -v '{abs_path}:{container_path}' alpine:latest ls -la {container_path}"
        )
        
        if success:
            print_success(f"Mount test passed: {abs_path} -> {container_path}")
        else:
            print_error(f"Mount test failed: {abs_path} -> {container_path}")
            print_error(f"Error: {stderr}")
    
    # Step 5: Simulate /createbot command
    print_step(5, "Simulating /createbot command")
    
    # This simulates what happens when user runs /createbot
    test_symbol = "eth"
    test_amount = "100"
    
    print_info(f"Simulating: /createbot with symbol='{test_symbol}', amount='{test_amount}'")
    
    # Check if container already exists (should be cleaned up)
    container_name = "ethusdt"  # Based on our naming logic
    
    success, stdout, stderr = run_command(f"docker ps -a --format '{{{{.Names}}}}' | grep '^{container_name}$'")
    if success and container_name in stdout:
        print_info(f"Container '{container_name}' exists - should be cleaned up")
    else:
        print_info(f"Container '{container_name}' does not exist - ready to create")
    
    # Step 6: Test actual bot creation (dry run)
    print_step(6, "Testing bot creation (dry run)")
    
    cmd = f"./bot.sh start {test_symbol} --api-key 'test_key' --api-secret 'test_secret' --amount {test_amount} --test-mode"
    print_info(f"Command that would be executed: {cmd}")
    
    # Don't actually run it to avoid creating containers
    print_info("Skipping actual execution to avoid container creation")
    
    print_success("All tests completed!")
    print()
    print("🚀 Ready to test /createbot in Telegram:")
    print("   1. Open Telegram and find your bot")
    print("   2. Send: /createbot")
    print("   3. Follow the wizard prompts")
    print("   4. Monitor with: docker logs telegram-bot -f")
    print()
    print("📋 Expected flow:")
    print("   1. Bot asks for symbol (e.g., 'eth')")
    print("   2. Bot asks for amount (e.g., '100')")
    print("   3. Bot asks for direction (LONG/SHORT)")
    print("   4. Bot creates and starts trading bot")
    print("   5. Bot sends confirmation message")
    
    return True

if __name__ == "__main__":
    test_createbot_flow()
