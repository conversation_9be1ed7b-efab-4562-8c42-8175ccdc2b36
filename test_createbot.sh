#!/bin/bash
# Test script for /createbot command debugging
# Tests step by step to identify Docker mount issues

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

echo "🧪 AutoTrader CreateBot Test Script"
echo "=================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_step() {
    echo -e "${BLUE}📋 Step $1: $2${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️ $1${NC}"
}

# Test 1: Check Docker
print_step "1" "Checking Docker"
if ! command -v docker &> /dev/null; then
    print_error "Docker not found"
    exit 1
fi

if ! docker info &> /dev/null; then
    print_error "Docker daemon not running"
    exit 1
fi
print_success "Docker is running"

# Test 2: Check directories
print_step "2" "Checking directories"
for dir in configs data logs; do
    if [[ -d "$dir" ]]; then
        print_success "Directory $dir exists"
        echo "   Path: $(cd "$dir" && pwd)"
    else
        print_error "Directory $dir missing"
        mkdir -p "$dir"
        print_success "Created directory $dir"
    fi
done

# Test 3: Test Docker mounts individually
print_step "3" "Testing Docker mounts individually"

test_mount() {
    local local_path="$1"
    local container_path="$2"
    local name="$3"
    
    echo "   Testing $name mount: $local_path -> $container_path"
    
    if docker run --rm -v "$local_path:$container_path" alpine:latest ls -la "$container_path" > /dev/null 2>&1; then
        print_success "$name mount works"
    else
        print_error "$name mount failed"
        echo "   Local path: $local_path"
        echo "   Container path: $container_path"
        return 1
    fi
}

# Get absolute paths
ABS_CONFIG_DIR="$(cd configs && pwd)"
ABS_DATA_DIR="$(cd data && pwd)"
ABS_LOGS_DIR="$(cd logs && pwd)"
ABS_CREDS_DIR="$HOME/.autotrader/credentials"

# Ensure credentials dir exists
mkdir -p "$ABS_CREDS_DIR"

# Test each mount
test_mount "$ABS_CONFIG_DIR" "/app/configs" "Config"
test_mount "$ABS_DATA_DIR" "/app/data" "Data"
test_mount "$ABS_LOGS_DIR" "/app/logs" "Logs"
test_mount "$ABS_CREDS_DIR" "/root/.autotrader/credentials" "Credentials"

# Test 4: Check container naming
print_step "4" "Testing container naming"
source src/core/shell_constants.sh

test_symbols=("eth" "ETH" "ETH/USDT:USDT" "btc" "BTC/USDT:USDT")
for symbol in "${test_symbols[@]}"; do
    container_name=$(get_container_name "$symbol")
    echo "   Symbol: $symbol -> Container: $container_name"
done

# Test 5: Check existing containers
print_step "5" "Checking existing containers"
echo "Current containers:"
docker ps -a --format "table {{.Names}}\t{{.Status}}\t{{.Image}}" | grep -E "(autotrader|telegram)" || echo "No autotrader containers found"

# Test 6: Cleanup test containers
print_step "6" "Cleaning up test containers"
test_containers=("ethusdt" "btcusdt" "ethusdtusdt")
for container in "${test_containers[@]}"; do
    if docker ps -a --format "{{.Names}}" | grep -q "^${container}$"; then
        echo "   Removing container: $container"
        docker stop "$container" 2>/dev/null || true
        docker rm "$container" 2>/dev/null || true
        print_success "Removed $container"
    else
        echo "   Container $container not found"
    fi
done

# Test 7: Test bot.sh start command with dry run
print_step "7" "Testing bot.sh start command (dry run)"
echo "Command that would be executed:"
echo "./bot.sh start eth --api-key 'test_key' --api-secret 'test_secret' --amount 100 --test-mode"

print_success "All tests completed!"
echo ""
echo "🚀 Ready to test /createbot command"
echo "   1. Start Telegram bot: ./bot.sh telegram"
echo "   2. Use /createbot in Telegram"
echo "   3. Monitor with: docker logs telegram-bot"
