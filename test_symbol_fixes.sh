#!/bin/bash
# Test script for symbol normalization fixes

set -e

# Source the shell constants
source src/core/shell_constants.sh

echo "🧪 Testing Symbol Normalization Fixes"
echo "======================================"

# Test cases for normalize_symbol
echo ""
echo "📊 Testing normalize_symbol function:"
echo "------------------------------------"

test_cases=(
    "btc|BTC/USDT:USDT"
    "BTC|BTC/USDT:USDT"
    "eth|ETH/USDT:USDT"
    "ETH|ETH/USDT:USDT"
    "ethusdt|ETH/USDT:USDT"
    "ETHUSDT|ETH/USDT:USDT"
    "btcusdt|BTC/USDT:USDT"
    "BTCUSDT|BTC/USDT:USDT"
    "solusdt|SOL/USDT:USDT"
    "SOLUSDT|SOL/USDT:USDT"
    "hyper|HYPER/USDT:USDT"
    "HYPER|HYPER/USDT:USDT"
    "hyperusdt|HYPER/USDT:USDT"
    "HYPERUSDT|HYPER/USDT:USDT"
    "BTC/USDT:USDT|BTC/USDT:USDT"
    "ETH/USDT:USDT|ETH/USDT:USDT"
)

all_passed=true

for test_case in "${test_cases[@]}"; do
    input="${test_case%|*}"
    expected="${test_case#*|}"
    
    result=$(normalize_symbol "$input")
    
    if [[ "$result" == "$expected" ]]; then
        echo "✅ '$input' → '$result'"
    else
        echo "❌ '$input' → '$result' (expected: '$expected')"
        all_passed=false
    fi
done

echo ""
echo "📦 Testing get_container_name function:"
echo "--------------------------------------"

container_test_cases=(
    "BTC/USDT:USDT|btcusdt"
    "ETH/USDT:USDT|ethusdt"
    "SOL/USDT:USDT|solusdt"
    "HYPER/USDT:USDT|hyperusdt"
    "btc|btcusdt"
    "eth|ethusdt"
    "ethusdt|ethusdt"
    "ETHUSDT|ethusdt"
    "btcusdt|btcusdt"
    "BTCUSDT|btcusdt"
)

for test_case in "${container_test_cases[@]}"; do
    input="${test_case%|*}"
    expected="${test_case#*|}"
    
    result=$(get_container_name "$input")
    
    if [[ "$result" == "$expected" ]]; then
        echo "✅ '$input' → '$result'"
    else
        echo "❌ '$input' → '$result' (expected: '$expected')"
        all_passed=false
    fi
done

echo ""
echo "🔄 Testing combined workflow:"
echo "----------------------------"

workflow_test_cases=(
    "btc"
    "eth"
    "sol"
    "ethusdt"
    "btcusdt"
    "solusdt"
    "ETHUSDT"
    "BTCUSDT"
    "SOLUSDT"
)

for symbol in "${workflow_test_cases[@]}"; do
    normalized=$(normalize_symbol "$symbol")
    container=$(get_container_name "$symbol")
    
    echo "🔄 '$symbol' → normalized: '$normalized' → container: '$container'"
    
    # Check for invalid patterns
    if [[ "$normalized" =~ USDT/USDT:USDT ]]; then
        echo "❌ Invalid normalization: contains USDT/USDT:USDT"
        all_passed=false
    fi
    
    if [[ "$container" =~ usdtusdt ]]; then
        echo "❌ Invalid container name: contains duplicate usdt"
        all_passed=false
    fi
done

echo ""
echo "======================================"
if [[ "$all_passed" == "true" ]]; then
    echo "🎉 All symbol normalization tests passed!"
    exit 0
else
    echo "❌ Some tests failed - please check the fixes"
    exit 1
fi
