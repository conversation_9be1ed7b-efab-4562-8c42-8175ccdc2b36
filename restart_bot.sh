#!/bin/bash
# AutoTrader Bot Restart Script
# Rebuild and restart Docker containers exactly like bot.sh

set -euo pipefail

# Version and basic config
SCRIPT_VERSION="3.2.0"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
DOCKER_CMD="docker"

# ===============================
# Load centralized constants and utilities
# ===============================

# Source shell constants and utilities
source "$SCRIPT_DIR/src/core/shell_constants.sh"

# Auto-load .env file if exists
if [[ -f ".env" ]]; then
    source .env
fi

# Environment variables with defaults
TELEGRAM_BOT_TOKEN="${TELEGRAM_BOT_TOKEN:-}"
TELEGRAM_CHAT_ID="${TELEGRAM_CHAT_ID:-}"

# ===============================
# Utility Functions (from bot.sh)
# ===============================

check_docker() {
    if ! command -v docker &> /dev/null; then
        echo "❌ Docker is not installed or not in PATH"
        echo "Please install Docker and try again"
        return 1
    fi

    if ! docker info &> /dev/null; then
        echo "❌ Docker daemon is not running"
        echo "Please start Docker and try again"
        return 1
    fi

    return 0
}

# ===============================
# Restart Functions
# ===============================

restart_telegram_bot() {
    echo "🔄 Restarting Telegram Bot (Docker)"
    echo "===================================="

    # Check Docker
    if ! check_docker; then
        echo "❌ Docker is required to restart Telegram bot"
        return 1
    fi

    # Check environment
    if [[ -z "$TELEGRAM_BOT_TOKEN" ]] || [[ -z "$TELEGRAM_CHAT_ID" ]]; then
        echo "❌ Missing Telegram credentials"
        echo ""
        echo "💡 Set environment variables:"
        echo "   export TELEGRAM_BOT_TOKEN='your_bot_token'"
        echo "   export TELEGRAM_CHAT_ID='your_chat_id'"
        return 1
    fi

    # Stop and remove existing container (same as bot.sh)
    echo "🛑 Stopping existing Telegram bot container..."
    if docker ps -a --format "{{.Names}}" | grep -q "^telegram-bot$"; then
        docker stop telegram-bot 2>/dev/null || true
        docker rm telegram-bot 2>/dev/null || true
        echo "✅ Existing container stopped and removed"
    else
        echo "ℹ️ No existing container found"
    fi

    # Rebuild Docker image
    echo "🔨 Rebuilding Docker image..."
    docker build -f Dockerfile.telegram -t "$TELEGRAM_IMAGE" . || {
        echo "❌ Failed to build Docker image"
        return 1
    }
    echo "✅ Docker image rebuilt successfully"

    # Start new Telegram bot container (exactly like bot.sh)
    echo "🐳 Starting new Telegram bot container..."

    # Ensure directories exist
    ensure_directories

    # Convert relative paths to absolute paths
    ABS_CONFIG_DIR="$(cd "$CONFIG_DIR" && pwd)"
    ABS_DATA_DIR="$(cd "$DATA_DIR" && pwd)"
    ABS_LOGS_DIR="$(cd "$LOGS_DIR" && pwd)"
    ABS_CREDENTIALS_DIR="$(cd "$CREDENTIALS_DIR" && pwd)"

    echo "📁 Volume mounts:"
    echo "   Config: $ABS_CONFIG_DIR -> /app/configs"
    echo "   Data: $ABS_DATA_DIR -> /app/data"
    echo "   Logs: $ABS_LOGS_DIR -> /app/logs"
    echo "   Credentials: $ABS_CREDENTIALS_DIR -> /root/.autotrader/credentials"

    docker run -d \
        --name telegram-bot \
        --restart unless-stopped \
        -e TELEGRAM_BOT_TOKEN="$TELEGRAM_BOT_TOKEN" \
        -e TELEGRAM_CHAT_ID="$TELEGRAM_CHAT_ID" \
        -e RUNNING_IN_CONTAINER="true" \
        -e HOST_CONFIG_DIR="$ABS_CONFIG_DIR" \
        -e HOST_DATA_DIR="$ABS_DATA_DIR" \
        -e HOST_LOGS_DIR="$ABS_LOGS_DIR" \
        -v "$ABS_CONFIG_DIR:/app/configs" \
        -v "$ABS_DATA_DIR:/app/data" \
        -v "$ABS_LOGS_DIR:/app/logs" \
        -v "$ABS_CREDENTIALS_DIR:/root/.autotrader/credentials" \
        -v /var/run/docker.sock:/var/run/docker.sock \
        "$TELEGRAM_IMAGE"

    if [[ $? -eq 0 ]]; then
        echo "✅ Telegram bot restarted successfully!"
        echo "📊 Container: telegram-bot"
        echo "🔗 Token: ${TELEGRAM_BOT_TOKEN:0:10}..."
        echo "💬 Chat ID: $TELEGRAM_CHAT_ID"
        echo ""
        echo "📋 Monitor with:"
        echo "   docker logs telegram-bot"
        echo "   ./bot.sh simple-logs telegram-bot"
        echo ""
        echo "🧪 Test with:"
        echo "   Send /help to your Telegram bot"
        echo "   Send /createbot to test wizard"

        # Show recent logs
        echo ""
        echo "📋 Recent logs:"
        sleep 3
        docker logs telegram-bot --tail 20
    else
        echo "❌ Failed to restart Telegram bot container"
        return 1
    fi
}

restart_trading_bot() {
    local symbol="$1"

    echo "🔄 Restarting Trading Bot: $symbol"
    echo "=================================="

    if [[ -z "$symbol" ]]; then
        echo "❌ Symbol is required"
        echo "Usage: $0 trading <symbol>"
        return 1
    fi

    # Stop existing trading bot
    echo "🛑 Stopping existing trading bot for $symbol..."
    ./bot.sh stop "$symbol" 2>/dev/null || true

    # Wait a moment for cleanup
    sleep 2

    # Start new trading bot
    echo "🚀 Starting new trading bot for $symbol..."
    ./bot.sh start "$symbol"
}

cleanup_all() {
    echo "🧹 Cleaning up all containers and images"
    echo "========================================"

    # Stop all autotrader containers
    echo "🛑 Stopping all autotrader containers..."
    docker ps --format "{{.Names}}" | grep -E "(telegram-bot|autotrader-)" | xargs -r docker stop

    # Remove all autotrader containers
    echo "🗑️ Removing all autotrader containers..."
    docker ps -a --format "{{.Names}}" | grep -E "(telegram-bot|autotrader-)" | xargs -r docker rm

    # Remove unused images
    echo "🧹 Cleaning up unused Docker images..."
    docker image prune -f

    echo "✅ Cleanup completed"
}

# ===============================
# Main Script Logic
# ===============================

show_help() {
    echo "🔄 AutoTrader Restart Script v$SCRIPT_VERSION"
    echo "============================================="
    echo ""
    echo "📋 Available Commands:"
    echo ""
    echo "🤖 Bot Management:"
    echo "   $0 telegram              🔄 Restart Telegram bot (rebuild + restart)"
    echo "   $0 trading <symbol>      🔄 Restart trading bot for symbol"
    echo "   $0 all                   🔄 Restart all bots"
    echo ""
    echo "🧹 Cleanup:"
    echo "   $0 cleanup               🧹 Stop and remove all containers"
    echo "   $0 clean                 🧹 Alias for cleanup"
    echo ""
    echo "📋 Info:"
    echo "   $0 help                  ❓ Show this help"
    echo "   $0 status                📊 Show system status"
    echo ""
    echo "💡 Examples:"
    echo "   $0 telegram              # Restart Telegram bot"
    echo "   $0 trading BTC           # Restart BTC trading bot"
    echo "   $0 cleanup               # Clean up all containers"
}

show_status() {
    echo "📊 AutoTrader System Status"
    echo "==========================="
    echo ""

    # Docker status
    if check_docker; then
        echo "✅ Docker: Running"
        echo ""
        echo "🐳 Running Containers:"
        docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep -E "(telegram-bot|autotrader-)" || echo "   No autotrader containers running"
        echo ""
        echo "📦 All Containers:"
        docker ps -a --format "table {{.Names}}\t{{.Status}}" | grep -E "(telegram-bot|autotrader-)" || echo "   No autotrader containers found"
    else
        echo "❌ Docker: Not available"
    fi

    echo ""
    echo "🔧 Environment:"
    [[ -n "$TELEGRAM_BOT_TOKEN" ]] && echo "✅ Telegram Token: Configured" || echo "⚠️ Telegram Token: Not configured"
    [[ -n "$TELEGRAM_CHAT_ID" ]] && echo "✅ Telegram Chat ID: Configured" || echo "⚠️ Telegram Chat ID: Not configured"
    echo "📁 Config Dir: $CONFIG_DIR"
    echo "📁 Data Dir: $DATA_DIR"
    echo "📁 Logs Dir: $LOGS_DIR"
}

# Main command processing
case "${1:-help}" in
    telegram)
        restart_telegram_bot
        ;;
    trading)
        if [[ -z "${2:-}" ]]; then
            echo "❌ Symbol is required for trading bot restart"
            echo "Usage: $0 trading <symbol>"
            exit 1
        fi
        restart_trading_bot "$2"
        ;;
    all)
        echo "🔄 Restarting all bots..."
        restart_telegram_bot
        echo ""
        echo "✅ All bots restarted"
        ;;
    cleanup|clean)
        cleanup_all
        ;;
    status)
        show_status
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        echo "❌ Unknown command: $1"
        echo "💡 Use '$0 help' for available commands"
        exit 1
        ;;
esac
