#!/usr/bin/env python3
"""Main Telegram Command Handler with clean modular architecture."""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Tuple

from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup, ForceReply
from telegram.ext import Application, CommandHandler, MessageHandler, CallbackQueryHandler, filters
from telegram.constants import ParseMode

# Import modular handlers
from .base_handler import BaseTelegramHandler
from .credential_handler import CredentialHandler
from .bot_management_handler import BotManagementHandler
from .wizard_handler import WizardHandler

from ..templates import TelegramTemplates


class MainTelegramHandler(BaseTelegramHandler):
    """Main Telegram command handler with clean modular architecture."""

    def __init__(self, bot_token: str, chat_id: int):
        super().__init__(bot_token, chat_id)

        # Initialize specialized handlers with error handling
        # Share the same session manager across all handlers
        try:
            print("   - Initializing CredentialHandler...")
            self.credential_handler = CredentialHandler(bot_token, chat_id)
            self.credential_handler.session_manager = self.session_manager  # Share session manager

            print("   - Initializing BotManagementHandler...")
            self.bot_management_handler = BotManagementHandler(bot_token, chat_id)
            self.bot_management_handler.session_manager = self.session_manager  # Share session manager

            print("   - Initializing WizardHandler...")
            self.wizard_handler = WizardHandler(bot_token, chat_id)
            self.wizard_handler.session_manager = self.session_manager  # Share session manager

            print("   - All handlers initialized successfully")
        except Exception as e:
            print(f"   ❌ Error initializing handlers: {e}")
            self.logger.error(f"Error initializing handlers: {e}", exc_info=True)
            # Create dummy handlers to prevent crashes
            self.credential_handler = None
            self.bot_management_handler = None
            self.wizard_handler = None
    
    def start(self):
        """Start the Telegram bot with polling"""
        try:
            print("🔧 Creating Telegram application...")
            self.application = Application.builder().token(self.bot_token).build()

            print("📝 Adding command handlers...")
            self._setup_handlers()

            print("🚀 Starting bot...")
            print(f"   Application: {self.application}")
            print(f"   Handlers registered: {len(self.application.handlers)}")

            # Debug handler details
            for group_id, handler_group in self.application.handlers.items():
                print(f"   Handler group {group_id}: {len(handler_group)} handlers")
                for j, handler in enumerate(handler_group):
                    print(f"     Handler {j}: {type(handler).__name__}")

            # Add debug for polling
            print("   Starting polling...")
            print(f"   Drop pending updates: False")
            print(f"   Allowed updates: ['message', 'callback_query']")

            # Add custom error handler with enhanced parsing error handling
            async def error_handler(update, context):
                error_msg = str(context.error)
                print(f"🚨 ERROR: {error_msg}")

                # Log detailed debug info for parsing errors
                if "can't parse entities" in error_msg.lower():
                    print(f"🔍 DEBUG: Received update: {update.update_id}")
                    print(f"   Update type: {type(update)}")
                    print(f"   Update dict: {update.to_dict()}")

                    if update.message:
                        print(f"   Message from user {update.effective_user.id}: {update.message.text}")
                        print(f"   Chat ID: {update.effective_chat.id}")
                        print(f"   Message ID: {update.message.message_id}")

                    self.logger.info(f"Debug update: {update.update_id} from user {update.effective_user.id}")

                self.logger.error(f"Update {update} caused error {context.error}")

            self.application.add_error_handler(error_handler)

            # Add debug for all updates received
            async def debug_all_updates(update, context):
                print(f"🔄 POLLING DEBUG: Received update {update.update_id}")
                print(f"   Update type: {type(update)}")
                if update.message:
                    print(f"   Message: {update.message.text}")
                    print(f"   From user: {update.effective_user.id}")
                    print(f"   Chat: {update.effective_chat.id}")
                    print(f"   Expected chat: {self.chat_id}")
                    print(f"   Chat matches: {update.effective_chat.id == self.chat_id}")
                if update.callback_query:
                    print(f"   Callback query: {update.callback_query.data}")
                # Continue processing
                return

            # Add this handler with highest priority
            from telegram.ext import TypeHandler
            from telegram import Update
            self.application.add_handler(TypeHandler(Update, debug_all_updates), group=-1)

            # Also add a simple message handler for debugging
            async def simple_debug(update, context):
                print(f"🔍 SIMPLE DEBUG: Got update {update.update_id}")
                if update.message:
                    print(f"   Text: {update.message.text}")
                return

            self.application.add_handler(MessageHandler(filters.ALL, simple_debug), group=-1)

            # Add debug for application events
            async def post_init(application):
                print("🚀 Application post_init called")

            async def post_shutdown(application):
                print("🛑 Application post_shutdown called")

            self.application.post_init = post_init
            self.application.post_shutdown = post_shutdown

            print("   Calling run_polling...")

            # Add custom update processor
            async def process_update(update, context):
                print(f"🔄 CUSTOM: Processing update {update.update_id}")
                if update.message:
                    print(f"   Message: '{update.message.text}'")
                    print(f"   From: {update.effective_user.id}")
                    print(f"   Chat: {update.effective_chat.id}")
                # Continue with normal processing
                return

            # Add the custom processor
            from telegram.ext import TypeHandler
            from telegram import Update
            self.application.add_handler(TypeHandler(Update, process_update), group=-2)

            self.application.run_polling(
                drop_pending_updates=True,
                allowed_updates=["message", "callback_query"],
                poll_interval=1.0,
                timeout=10
            )
            print("   run_polling completed")

        except Exception as e:
            self.logger.error(f"Error starting bot: {e}", exc_info=True)
            raise
    
    def _setup_handlers(self):
        """Setup all command handlers"""
        print("📝 Setting up command handlers...")

        # Basic commands
        print("   - Adding /start handler")
        self.application.add_handler(CommandHandler("start", self.handle_start))
        print("   - Adding /help handler")
        self.application.add_handler(CommandHandler("help", self.handle_help))
        print("   - Adding /cancel handler")
        if self.wizard_handler:
            self.application.add_handler(CommandHandler("cancel", self.wizard_handler.handle_cancel))
        else:
            print("   ⚠️ Wizard handler not available for cancel")

        # Add test handler
        print("   - Adding /test handler")
        self.application.add_handler(CommandHandler("test", self.handle_test))

        # Add self-test command
        print("   - Adding /selftest handler")
        self.application.add_handler(CommandHandler("selftest", self.handle_selftest))

        # Add simulate command
        print("   - Adding /simulate handler")
        self.application.add_handler(CommandHandler("simulate", self.handle_simulate))

        # Add testcmd command
        print("   - Adding /testcmd handler")
        self.application.add_handler(CommandHandler("testcmd", self.handle_testcmd))

        # Credential commands
        if self.credential_handler:
            print("   - Adding credential handlers")
            self.application.add_handler(CommandHandler("addcreds", self.credential_handler.handle_addcreds_wizard))
            self.application.add_handler(CommandHandler("listcreds", self.credential_handler.handle_listcreds))
            self.application.add_handler(CommandHandler("showcreds", self.credential_handler.handle_showcreds))
            self.application.add_handler(CommandHandler("deletecreds", self.credential_handler.handle_deletecreds))
        else:
            print("   ⚠️ Credential handler not available")

        # Bot management commands
        if self.bot_management_handler and self.wizard_handler:
            print("   - Adding bot management handlers")
            self.application.add_handler(CommandHandler("createbot", self.wizard_handler.handle_createbot_wizard))
            self.application.add_handler(CommandHandler("startbot", self.bot_management_handler.handle_start_bot))
            self.application.add_handler(CommandHandler("list", self.bot_management_handler.handle_list_bots))
            self.application.add_handler(CommandHandler("status", self.bot_management_handler.handle_status_bot))
            self.application.add_handler(CommandHandler("stop", self.bot_management_handler.handle_stop_bot))
            self.application.add_handler(CommandHandler("stopall", self.bot_management_handler.handle_stop_all_bots))
            self.application.add_handler(CommandHandler("logs", self.bot_management_handler.handle_logs_bot))
            self.application.add_handler(CommandHandler("restart", self.bot_management_handler.handle_restart_bot))
        else:
            print("   ⚠️ Bot management handlers not available")



        # Message handlers for wizard steps
        print("   - Adding message handler")
        # Remove chat ID filtering to allow all users
        self.application.add_handler(MessageHandler(
            filters.TEXT & ~filters.COMMAND,
            self.handle_message
        ))

        # Callback query handler
        print("   - Adding callback query handler")
        self.application.add_handler(CallbackQueryHandler(self.handle_callback_query))

        # Add debug handler for all updates
        print("   - Adding debug update handler")
        self.application.add_handler(MessageHandler(filters.ALL, self.debug_update_handler), group=999)
    
    async def handle_command(self, update, context) -> None:
        """Handle command - required by abstract base class"""
        # This method is required by TelegramBaseHandler but not used in our architecture
        # Commands are handled by specific handlers registered in _setup_handlers
        pass

    async def handle_start(self, update: Update, context) -> None:
        """Handle /start command with enhanced welcome message"""
        self.logger.info(f"handle_start called by user {update.effective_user.id}")
        try:
            # Use enhanced welcome template
            self.logger.info("Getting welcome message template")
            template = TelegramTemplates.welcome_message()

            # Create inline keyboard
            self.logger.info("Creating inline keyboard")
            keyboard = self._create_inline_keyboard(template.keyboard) if template.keyboard else None

            self.logger.info("Sending welcome message")
            await self._send_safe_message(
                update.message.reply_text,
                template.content,
                parse_mode=ParseMode.HTML,
                reply_markup=keyboard
            )
            self.logger.info("Welcome message sent successfully")

        except Exception as e:
            self.logger.error(f"Error in handle_start: {e}", exc_info=True)
            # Fallback to simple message
            try:
                await update.message.reply_text(
                    "🤖 **Welcome to AutoTrader Bot!**\n\n"
                    "Use /help to see available commands.",
                    parse_mode=ParseMode.MARKDOWN
                )
            except Exception as e2:
                self.logger.error(f"Error in fallback message: {e2}", exc_info=True)
    
    async def handle_help(self, update: Update, context) -> None:
        """Handle /help command with enhanced help system"""
        self.logger.info(f"handle_help called by user {update.effective_user.id}")
        try:
            # Use enhanced help template
            self.logger.info("Getting help template")
            template = TelegramTemplates.help_main()

            # Create inline keyboard
            self.logger.info("Creating help keyboard")
            keyboard = self._create_inline_keyboard(template.keyboard) if template.keyboard else None

            self.logger.info("Sending help message")
            self.logger.info(f"Template content length: {len(template.content)}")
            self.logger.info(f"Template content preview: {template.content[:200]}...")

            await self._send_safe_message(
                update.message.reply_text,
                template.content,
                parse_mode=ParseMode.HTML,
                reply_markup=keyboard
            )
            self.logger.info("Help message sent successfully")

        except Exception as e:
            self.logger.error(f"Error in handle_help: {e}", exc_info=True)
            # Fallback to simple help
            try:
                await update.message.reply_text(
                    "📚 **Help**\n\n"
                    "Use /addcreds to add credentials\n"
                    "Use /createbot to create a trading bot\n"
                    "Use /list to see all bots",
                    parse_mode=ParseMode.MARKDOWN
                )
            except Exception as e2:
                self.logger.error(f"Error in fallback help: {e2}", exc_info=True)

    async def handle_test(self, update: Update, context) -> None:
        """Simple test handler to verify bot is working"""
        self.logger.info(f"handle_test called by user {update.effective_user.id}")
        try:
            await update.message.reply_text(
                "✅ **Test Successful!**\n\n"
                f"User ID: {update.effective_user.id}\n"
                f"Chat ID: {update.effective_chat.id}\n"
                f"Message: {update.message.text}",
                parse_mode=ParseMode.MARKDOWN
            )
            self.logger.info("Test message sent successfully")
        except Exception as e:
            self.logger.error(f"Error in handle_test: {e}", exc_info=True)

    async def handle_selftest(self, update: Update, context) -> None:
        """Handle /selftest command - bot sends messages to itself and processes them"""
        self.logger.info(f"handle_selftest called by user {update.effective_user.id}")

        try:
            # Send initial response
            await self.safe_sender.send_safe_message(
                update.message.reply_text,
                "🤖 **Self-Test Mode Activated**\n\nBot sẽ tự gửi và xử lý các commands...",
                parse_mode=ParseMode.MARKDOWN
            )

            # Commands to test
            test_commands = [
                "/help",
                "/createbot",
                "/addcreds",
                "/list",
                "/status"
            ]

            for i, cmd in enumerate(test_commands, 1):
                await asyncio.sleep(2)  # Delay between tests

                # Notify about current test
                await self.safe_sender.send_safe_message(
                    update.message.reply_text,
                    f"🔄 **Self-Test {i}/5**: Testing `{cmd}`...",
                    parse_mode=ParseMode.MARKDOWN
                )

                # Actually simulate and process the command
                await self.simulate_user_message(
                    chat_id=update.effective_chat.id,
                    message_text=cmd,
                    user_id=999999999  # Fake user ID
                )

                await asyncio.sleep(1)  # Small delay after processing

            # Final summary
            await self.safe_sender.send_safe_message(
                update.message.reply_text,
                "✅ **Self-Test Complete**\n\nTất cả commands đã được test! Kiểm tra logs để xem kết quả.",
                parse_mode=ParseMode.MARKDOWN
            )

        except Exception as e:
            self.logger.error(f"Error in handle_selftest: {e}", exc_info=True)

    async def simulate_user_message(self, chat_id: int, message_text: str, user_id: int = None):
        """Simulate a user message and actually process it through handlers"""
        if user_id is None:
            user_id = chat_id

        try:
            from telegram import Update, Message, User, Chat
            from telegram.constants import ChatType
            import time

            # Log simulation start
            print(f"🔄 SIMULATED: Creating fake message from user {user_id}")
            print(f"   Message: '{message_text}'")
            print(f"   Chat ID: {chat_id}")
            self.logger.info(f"🔄 SIMULATED: Message from user {user_id}: {message_text}")

            # Create fake User object
            fake_user = User(
                id=user_id,
                is_bot=False,
                first_name="SimulatedUser",
                username="simulated_user"
            )

            # Create fake Chat object
            fake_chat = Chat(
                id=chat_id,
                type=ChatType.PRIVATE
            )

            # Create fake Message object
            fake_message = Message(
                message_id=int(time.time()),
                date=int(time.time()),
                chat=fake_chat,
                from_user=fake_user,
                text=message_text
            )

            # Create fake Update object
            fake_update = Update(
                update_id=int(time.time()),
                message=fake_message
            )

            # Process the fake update through the application's handlers
            print(f"🔄 SIMULATED: Processing fake update through handlers...")
            self.logger.info(f"🔄 SIMULATED: Processing fake update through handlers...")

            # Process the update
            await self.application.process_update(fake_update)

            print(f"✅ SIMULATED: Message processing complete")
            self.logger.info(f"✅ SIMULATED: Message processing complete")

        except Exception as e:
            print(f"❌ SIMULATED: Error processing fake message: {e}")
            self.logger.error(f"❌ SIMULATED: Error processing fake message: {e}", exc_info=True)

    async def handle_simulate(self, update: Update, context) -> None:
        """Handle /simulate command - simulate user messages"""
        self.logger.info(f"handle_simulate called by user {update.effective_user.id}")

        try:
            # Get message to simulate from command args
            if context.args:
                message_to_simulate = ' '.join(context.args)
            else:
                message_to_simulate = "/help"  # Default

            # Send response about what we're simulating
            await self.safe_sender.send_safe_message(
                update.message.reply_text,
                f"🎭 **Simulation Mode**\n\nSimulating message: `{message_to_simulate}`\n\nCheck logs for simulation details...",
                parse_mode=ParseMode.MARKDOWN
            )

            # Simulate the message
            await self.simulate_user_message(
                chat_id=update.effective_chat.id,
                message_text=message_to_simulate,
                user_id=999999999  # Fake user ID for simulation
            )

            # Send completion message
            await self.safe_sender.send_safe_message(
                update.message.reply_text,
                f"✅ **Simulation Complete**\n\nMessage `{message_to_simulate}` has been simulated and logged!",
                parse_mode=ParseMode.MARKDOWN
            )

        except Exception as e:
            self.logger.error(f"Error in handle_simulate: {e}", exc_info=True)

    async def handle_testcmd(self, update: Update, context) -> None:
        """Handle /testcmd command - test a specific command"""
        self.logger.info(f"handle_testcmd called by user {update.effective_user.id}")

        try:
            # Get command to test from args
            if not context.args:
                await self.safe_sender.send_safe_message(
                    update.message.reply_text,
                    "❌ **Usage**: `/testcmd <command>`\n\n**Examples:**\n• `/testcmd /help`\n• `/testcmd /createbot`\n• `/testcmd /list`",
                    parse_mode=ParseMode.MARKDOWN
                )
                return

            command_to_test = ' '.join(context.args)

            # Send notification
            await self.safe_sender.send_safe_message(
                update.message.reply_text,
                f"🧪 **Testing Command**: `{command_to_test}`\n\nProcessing...",
                parse_mode=ParseMode.MARKDOWN
            )

            # Actually simulate and process the command
            await self.simulate_user_message(
                chat_id=update.effective_chat.id,
                message_text=command_to_test,
                user_id=888888888  # Different fake user ID for testcmd
            )

            # Send completion message
            await self.safe_sender.send_safe_message(
                update.message.reply_text,
                f"✅ **Test Complete**: `{command_to_test}`\n\nKiểm tra logs và messages phía trên để xem kết quả!",
                parse_mode=ParseMode.MARKDOWN
            )

        except Exception as e:
            self.logger.error(f"Error in handle_testcmd: {e}", exc_info=True)

    async def debug_update_handler(self, update: Update, context) -> None:
        """Debug handler to log all updates"""
        print(f"🔍 DEBUG: Received update: {update.update_id}")
        print(f"   Update type: {type(update)}")
        print(f"   Update dict: {update.to_dict()}")

        if update.message:
            print(f"   Message from user {update.effective_user.id}: {update.message.text}")
            print(f"   Chat ID: {update.effective_chat.id}")
            print(f"   Message ID: {update.message.message_id}")
        if update.callback_query:
            print(f"   Callback query from user {update.effective_user.id}: {update.callback_query.data}")

        self.logger.info(f"Debug update: {update.update_id} from user {update.effective_user.id}")

        # Always continue processing
        return
    
    async def handle_message(self, update: Update, context) -> None:
        """Handle text messages (wizard steps)"""
        user_id = update.effective_user.id

        # Debug logging
        is_in_wizard = self._is_in_wizard(user_id)
        current_step = self._get_wizard_step(user_id)
        message_text = update.message.text
        print(f"🔍 DEBUG: Message handler - User {user_id}: message='{message_text}', in_wizard={is_in_wizard}, step={current_step}")
        print(f"🔍 DEBUG: Session manager ID: {id(self.session_manager)}")
        print(f"🔍 DEBUG: Session data: {self.session_manager.sessions.get(user_id, 'No session')}")
        self.logger.info(f"🔍 Message handler - User {user_id}: in_wizard={is_in_wizard}, step={current_step}")

        if not is_in_wizard:
            await self._send_info_message(
                update,
                "Sử dụng `/help` để xem danh sách lệnh có sẵn"
            )
            return

        # Get current wizard step
        step = current_step

        # Route to appropriate handler based on wizard type
        if step and step.startswith('addcreds_'):
            self.logger.info(f"🔄 Routing to credential handler for step: {step}")
            await self.credential_handler.handle_addcreds_wizard_step(update, context)
        elif step and step.startswith('createbot_'):
            self.logger.info(f"🔄 Routing to wizard handler for step: {step}")
            await self.wizard_handler.handle_createbot_wizard_step(update, context)
        else:
            self.logger.warning(f"❌ Invalid wizard step: {step}")
            await self._send_error_message(update, "Wizard step không hợp lệ")
            self._clear_wizard(user_id)
    
    async def handle_callback_query(self, update: Update, context) -> None:
        """Handle callback queries from inline keyboards"""
        query = update.callback_query
        await query.answer()
        
        data = query.data
        
        # Route callback to appropriate handler
        if data.startswith("help_"):
            await self._handle_help_callback(query, data)
        elif data.startswith("createbot_"):
            await self._handle_createbot_callback(query, data)
        elif data.startswith("stop_"):
            await self._handle_stop_callback(query, data)
        elif data.startswith("delete_creds_"):
            await self._handle_delete_creds_callback(query, data)
        elif data.startswith("stopall_"):
            await self._handle_stopall_callback(query, data)
        elif data.startswith("bot_") or data.startswith("creds_"):
            await self._handle_generic_callback(query, data)
        else:
            await query.edit_message_text("❌ Callback không hợp lệ")
    
    async def _handle_help_callback(self, query, data: str) -> None:
        """Handle help-related callbacks with enhanced templates"""
        try:
            template = None

            if data == "help_main":
                template = TelegramTemplates.help_main()
            elif data == "help_credentials":
                template = TelegramTemplates.help_credentials()
            elif data == "help_bots":
                template = TelegramTemplates.help_bots()
            elif data == "help_monitoring":
                template = TelegramTemplates.help_monitoring()
            elif data == "help_getting_started":
                template = TelegramTemplates.help_getting_started()
            elif data == "help_faq":
                template = TelegramTemplates.help_faq()
            else:
                await query.edit_message_text(
                    "❌ Tùy chọn không hợp lệ",
                    parse_mode=ParseMode.HTML
                )
                return

            if template:
                keyboard = self._create_inline_keyboard(template.keyboard) if template.keyboard else None
                await query.edit_message_text(
                    template.content,
                    parse_mode=ParseMode.HTML,
                    reply_markup=keyboard
                )

        except Exception as e:
            self.logger.error(f"Error in help callback: {e}")
            await query.edit_message_text(
                "❌ Có lỗi xảy ra khi hiển thị help",
                parse_mode=ParseMode.HTML
            )

    async def _handle_stopall_callback(self, query, data: str) -> None:
        """Handle stopall confirmation callbacks"""
        try:
            if data == "stopall_confirm":
                await self.bot_management_handler._execute_stop_all(query)
            elif data == "stopall_cancel":
                await query.edit_message_text(
                    "❌ **Stop All Cancelled**\n\nNo bots were stopped.",
                    parse_mode=ParseMode.MARKDOWN
                )
            else:
                await query.edit_message_text(
                    "❌ Invalid stopall callback",
                    parse_mode=ParseMode.HTML
                )

        except Exception as e:
            self.logger.error(f"Error in stopall callback: {e}")
            await query.edit_message_text(
                "❌ Error processing stopall request",
                parse_mode=ParseMode.HTML
            )

    async def _handle_generic_callback(self, query, data: str) -> None:
        """Handle generic callbacks for bot and credential actions"""
        try:
            if data.startswith("bot_"):
                # Delegate bot-related callbacks to bot management handler
                if self.bot_management_handler:
                    if data == "bot_list":
                        await self.bot_management_handler.handle_list_callback(query)
                    elif data == "bot_start_all":
                        await self.bot_management_handler.handle_startall_callback(query)
                    elif data == "bot_stop_all":
                        await self.bot_management_handler.handle_stopall_callback(query, "stopall_confirm")
                    elif data == "bot_clean_stopped":
                        await self.bot_management_handler.handle_clean_stopped_callback(query)
                    elif data == "bot_create":
                        # Start create bot wizard
                        await query.edit_message_text(
                            "🚀 Starting bot creation wizard...\n\nUse /createbot command to begin.",
                            parse_mode=None
                        )
                    elif data == "bot_stats":
                        await query.edit_message_text(
                            "📊 Use /status command to view detailed statistics.",
                            parse_mode=None
                        )
                    else:
                        await query.edit_message_text(
                            "❌ Bot callback not implemented yet",
                            parse_mode=None
                        )
                else:
                    await query.edit_message_text(
                        "❌ Bot management handler không khả dụng",
                        parse_mode=None
                    )

            elif data.startswith("creds_"):
                # Credential-related callbacks
                if data == "creds_add":
                    await query.edit_message_text(
                        "🔑 **Starting Credential Wizard**\n\n"
                        "Please use the command: /addcreds\n\n"
                        "This will start the interactive credential setup wizard.",
                        parse_mode=ParseMode.MARKDOWN
                    )
                elif data == "creds_list":
                    await query.edit_message_text(
                        "📋 **View Credentials**\n\n"
                        "Please use the command: /listcreds\n\n"
                        "This will show all your stored credential profiles.",
                        parse_mode=ParseMode.MARKDOWN
                    )
                else:
                    await query.edit_message_text(
                        "❌ Credential callback not implemented yet",
                        parse_mode=ParseMode.HTML
                    )
            else:
                await query.edit_message_text(
                    "❌ Unknown callback",
                    parse_mode=ParseMode.HTML
                )

        except Exception as e:
            self.logger.error(f"Error in generic callback: {e}")
            await query.edit_message_text(
                "❌ Error processing callback",
                parse_mode=ParseMode.HTML
            )
    
    async def _handle_createbot_callback(self, query, data: str) -> None:
        """Handle createbot-related callbacks"""
        if data.startswith("createbot_profile_"):
            profile = data.replace("createbot_profile_", "")
            await self.wizard_handler.handle_createbot_profile_callback(query, profile)
        elif data.startswith("createbot_direction_"):
            direction = data.replace("createbot_direction_", "")
            await self.wizard_handler.handle_createbot_direction_callback(query, direction)
        elif data.startswith("createbot_testmode_"):
            test_mode = data.replace("createbot_testmode_", "")
            await self.wizard_handler.handle_createbot_testmode_callback(query, test_mode)
        elif data == "createbot_confirm":
            await self.wizard_handler.handle_createbot_confirm(query)
        elif data == "createbot_cancel":
            user_id = query.from_user.id
            self._clear_wizard(user_id)
            await query.edit_message_text("❌ **Đã hủy tạo bot**", parse_mode=ParseMode.MARKDOWN)
    
    async def _handle_stop_callback(self, query, data: str) -> None:
        """Handle stop-related callbacks"""
        if data.startswith("stop_confirm_"):
            symbol = data.replace("stop_confirm_", "")
            await self.bot_management_handler.handle_stop_confirm(query, symbol)
        elif data == "stop_cancel":
            await query.edit_message_text("❌ **Đã hủy dừng bot**", parse_mode=ParseMode.MARKDOWN)
    
    async def _handle_delete_creds_callback(self, query, data: str) -> None:
        """Handle credential deletion callbacks"""
        # Pass all delete_creds_ callbacks to credential handler
        await self.credential_handler.handle_delete_creds_callback(query, data)
    



# For backward compatibility
ImprovedTelegramCommandHandler = MainTelegramHandler
