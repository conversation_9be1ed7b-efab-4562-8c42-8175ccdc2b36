#!/usr/bin/env python3
"""Bot management handler for Telegram bot."""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Tuple

from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup, ForceReply
from telegram.constants import ParseMode

from .base_handler import BaseTelegramHandler
from ....core.credential_utils import list_profiles
from ..templates import TelegramTemplates
import re
import json
from datetime import datetime, timedelta


class BotManagementHandler(BaseTelegramHandler):
    """Handler for bot management operations."""

    async def handle_command(self, update, context) -> None:
        """Handle command - required by abstract base class"""
        pass

    def _parse_bot_list_output(self, output: str) -> List[Dict[str, Any]]:
        """Parse bot.sh list output into structured data"""
        containers = []
        lines = output.strip().split('\n')

        # Find the data section (after the header)
        data_started = False
        for line in lines:
            line = line.strip()

            # Skip header and separator lines
            if '==' in line or 'NAME' in line and 'STATUS' in line:
                data_started = True
                continue
            if '--' in line:
                continue
            if line.startswith('📈 Summary:') or line.startswith('📊 Summary:'):
                break

            if data_started and line and not line.startswith('⚠️'):
                # Parse container line: NAME STATUS CREATED
                parts = line.split()
                if len(parts) >= 3:
                    name = parts[0]
                    status_part = parts[1]

                    # Extract status (remove emoji)
                    status = 'running' if '🟢' in status_part else 'stopped'

                    # Extract created date (remaining parts)
                    created = ' '.join(parts[2:])

                    # Determine symbol from container name
                    symbol = self._extract_symbol_from_name(name)

                    container = {
                        'name': name,
                        'symbol': symbol,
                        'status': status,
                        'created': created,
                        'uptime': self._calculate_uptime(created) if status == 'running' else None
                    }

                    containers.append(container)

        return containers

    def _extract_symbol_from_name(self, container_name: str) -> str:
        """Extract trading symbol from container name"""
        # Container names follow pattern: {symbol}usdt or just {symbol}
        # Examples: hyperusdt, btcusdt, ethusdt, hackingtool

        name = container_name.lower()

        # Remove common suffixes and prefixes
        name = re.sub(r'[-_](bot|trader|trading|container)$', '', name)
        name = re.sub(r'^(bot|trader|trading)[-_]', '', name)

        # Remove 'usdt' suffix if present (new naming convention)
        if name.endswith('usdt'):
            symbol = name[:-4]  # Remove 'usdt'
        else:
            symbol = name

        # Handle special cases where container name doesn't match symbol
        special_cases = { }

        if symbol in special_cases:
            return special_cases[symbol]

        return symbol.upper()

    def _calculate_uptime(self, created_str: str) -> str:
        """Calculate uptime from created timestamp"""
        try:
            # Parse created timestamp (format: 2025-05-30 23:47:41)
            created = datetime.strptime(created_str, '%Y-%m-%d %H:%M:%S')
            now = datetime.now()
            uptime = now - created

            days = uptime.days
            hours, remainder = divmod(uptime.seconds, 3600)
            minutes, _ = divmod(remainder, 60)

            if days > 0:
                return f"{days}d {hours}h {minutes}m"
            elif hours > 0:
                return f"{hours}h {minutes}m"
            else:
                return f"{minutes}m"

        except Exception:
            return "Unknown"

    def _filter_trading_containers(self, containers: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Filter out non-trading containers (system, db, etc.)"""
        # List of container names/patterns to exclude
        exclude_patterns = [
            'postgres', 'redis', 'mysql', 'mongodb',
            'nginx', 'apache', 'traefik',
            'prometheus', 'grafana', 'elasticsearch',
            'rabbitmq', 'kafka', 'zookeeper',
            'docker', 'registry', 'portainer',
            'telegram-bot'  # Exclude the telegram bot itself
        ]

        filtered = []
        for container in containers:
            name = container['name'].lower()

            # Skip if matches exclude patterns
            if any(pattern in name for pattern in exclude_patterns):
                continue

            # Skip if it's clearly a system container
            if name.startswith(('system-', 'infra-', 'monitoring-')):
                continue

            filtered.append(container)

        return filtered
    
    async def handle_start_bot(self, update: Update, context) -> None:
        """Handle /start command for starting trading bots"""
        if len(context.args) < 2:
            await self._send_error_message(
                update,
                "Sử dụng: /startbot <symbol> <amount> [test]"
            )
            return

        symbol = context.args[0].upper()
        amount = context.args[1]
        
        # Validate inputs
        if not self._validate_symbol(symbol):
            await self._send_error_message(update, f"Symbol không hợp lệ: {symbol}")
            return
        
        if not self._validate_amount(amount):
            await self._send_error_message(update, f"Amount không hợp lệ: {amount}")
            return
        
        try:
            # Build command arguments
            cmd_args = [self.bot_script_path, "start", symbol, "--amount", amount]
            
            # Add additional arguments
            for arg in context.args[2:]:
                if arg.lower() in ["test", "testmode", "test_mode"]:
                    cmd_args.append("--test-mode")
                else:
                    cmd_args.append(arg)
            
            # Start bot using bot.sh
            result = await self._execute_botsh_command(cmd_args)
            
            if result[0] == 0:
                await self._send_safe_message(
                    update.message.reply_text,
                    f"🚀 **Bot đã khởi động!**\n\n"
                    f"Symbol: `{symbol}`\n"
                    f"Amount: `${amount}`\n\n"
                    f"Sử dụng `/status {symbol}` để xem trạng thái.",
                    parse_mode=ParseMode.MARKDOWN
                )
            else:
                await self._send_error_message(update, result[2] or result[1])
                
        except Exception as e:
            self.logger.error(f"Error starting bot: {e}")
            await self._send_error_message(update, str(e))
    
    async def handle_stop_bot(self, update: Update, context) -> None:
        """Handle /stop command for stopping trading bots"""
        if not context.args:
            await self._send_error_message(update, "Sử dụng: /stop <symbol>")
            return

        symbol = context.args[0].upper()
        
        try:
            # Create confirmation keyboard
            keyboard = [
                [
                    InlineKeyboardButton("✅ Xác nhận", callback_data=f"stop_confirm_{symbol}"),
                    InlineKeyboardButton("❌ Hủy", callback_data="stop_cancel")
                ]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            await update.message.reply_text(
                f"⚠️ Xác nhận dừng bot\n\n"
                f"Bạn có chắc muốn dừng bot {symbol}?",
                reply_markup=reply_markup,
                parse_mode=None  # Remove markdown to avoid parsing errors
            )
            
        except Exception as e:
            self.logger.error(f"Error in handle_stop_bot: {e}")
            await self._send_error_message(update, str(e))
    
    async def handle_stop_confirm(self, query, symbol: str) -> None:
        """Handle bot stop confirmation"""
        try:
            # Step 1: Stop the container
            stop_result = await self._execute_botsh_command([
                self.bot_script_path, "stop", symbol, "--force"
            ])

            if stop_result[0] == 0:
                # Step 2: Remove the container after stopping
                remove_result = await self._execute_botsh_command([
                    self.bot_script_path, "remove", symbol, "--force"
                ])

                if remove_result[0] == 0:
                    await query.edit_message_text(
                        f"✅ Bot {symbol} đã được dừng và xóa thành công",
                        parse_mode=None
                    )
                else:
                    # Stop succeeded but remove failed
                    await query.edit_message_text(
                        f"⚠️ Bot {symbol} đã được dừng nhưng không thể xóa container\n"
                        f"Lỗi: {remove_result[2] or remove_result[1]}",
                        parse_mode=None
                    )
            else:
                # Stop failed
                error_msg = (stop_result[2] or stop_result[1])
                await query.edit_message_text(
                    f"❌ Lỗi dừng bot: {error_msg}",
                    parse_mode=None
                )

        except Exception as e:
            self.logger.error(f"Error stopping bot: {e}")
            await query.edit_message_text(
                f"❌ Lỗi: {str(e)}",
                parse_mode=None
            )
    
    async def handle_list_bots(self, update: Update, context) -> None:
        """Handle /list command for listing active bots with enhanced formatting"""
        try:
            # Get bot list from bot.sh
            result = await self._execute_botsh_command([self.bot_script_path, "list"])

            if result[0] != 0:
                await self._send_error_message(update, result[2] or result[1])
                return

            # Parse the output into structured data
            all_containers = self._parse_bot_list_output(result[1])

            # Filter to only trading containers
            trading_containers = self._filter_trading_containers(all_containers)

            # Enhance container data with additional info
            enhanced_containers = []
            for container in trading_containers:
                enhanced = await self._enhance_container_info(container)
                enhanced_containers.append(enhanced)

            # Get overall stats (if available)
            stats = await self._get_trading_stats(enhanced_containers)

            # Use enhanced template
            template = TelegramTemplates.bot_list_enhanced(enhanced_containers, stats)

            # Send message with inline keyboard
            keyboard = self._create_inline_keyboard(template.keyboard) if template.keyboard else None

            await update.message.reply_text(
                template.content,
                parse_mode=ParseMode.HTML,
                reply_markup=keyboard
            )

        except Exception as e:
            self.logger.error(f"Error listing bots: {e}")
            await self._send_error_message(update, str(e))

    async def _enhance_container_info(self, container: Dict[str, Any]) -> Dict[str, Any]:
        """Enhance container info with additional details"""
        enhanced = container.copy()

        try:
            # Try to get more detailed status
            status_result = await self._execute_botsh_command([
                self.bot_script_path, "status", container['name']
            ])

            if status_result[0] == 0:
                # Parse status output for additional info
                status_info = self._parse_status_output(status_result[1])
                enhanced.update(status_info)

        except Exception as e:
            self.logger.debug(f"Could not enhance info for {container['name']}: {e}")

        return enhanced

    def _parse_status_output(self, output: str) -> Dict[str, Any]:
        """Parse bot status output for additional information"""
        info = {}

        try:
            # Look for common patterns in status output
            lines = output.split('\n')

            for line in lines:
                line = line.strip()

                # Extract configuration info
                if 'amount:' in line.lower():
                    amount_match = re.search(r'amount[:\s]+\$?(\d+(?:\.\d+)?)', line, re.IGNORECASE)
                    if amount_match:
                        info.setdefault('config', {})['amount'] = amount_match.group(1)

                if 'direction:' in line.lower():
                    direction_match = re.search(r'direction[:\s]+(\w+)', line, re.IGNORECASE)
                    if direction_match:
                        info.setdefault('config', {})['direction'] = direction_match.group(1)

                if 'test' in line.lower() and 'mode' in line.lower():
                    info.setdefault('config', {})['test_mode'] = True

                # Extract performance info
                if 'pnl' in line.lower() or 'profit' in line.lower():
                    # Match patterns like: $0.00, +1.23, -2.45, $+1.23, etc.
                    pnl_match = re.search(r'[\$]?([\+\-]?\d+(?:\.\d+)?)', line)
                    if pnl_match:
                        pnl_value = pnl_match.group(1)
                        # Store the numeric value without $ sign
                        info.setdefault('performance', {})['pnl'] = pnl_value

                if 'trades' in line.lower():
                    trades_match = re.search(r'(\d+)', line)
                    if trades_match:
                        info.setdefault('performance', {})['trades'] = trades_match.group(1)

        except Exception as e:
            self.logger.debug(f"Error parsing status output: {e}")

        return info

    async def _get_trading_stats(self, containers: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Calculate overall trading statistics"""
        stats = {
            'total_pnl': 0.0,
            'total_trades': 0,
            'win_rate': 0.0
        }

        try:
            total_pnl = 0.0
            total_trades = 0

            for container in containers:
                perf = container.get('performance', {})

                if perf.get('pnl') is not None:
                    try:
                        # Remove $ sign, + sign, and any whitespace
                        pnl_str = str(perf['pnl']).replace('$', '').replace('+', '').strip()
                        # Handle empty string cases
                        if pnl_str:
                            pnl = float(pnl_str)
                            total_pnl += pnl
                    except (ValueError, TypeError) as e:
                        self.logger.debug(f"Error parsing PnL '{perf['pnl']}': {e}")
                        pass

                if perf.get('trades'):
                    try:
                        trades = int(perf['trades'])
                        total_trades += trades
                    except ValueError:
                        pass

            stats['total_pnl'] = f"${total_pnl:+.2f}" if total_pnl != 0 else "$0.00"
            stats['total_trades'] = total_trades

            # Calculate win rate based on actual data
            if total_trades > 0:
                # Count winning trades (positive PnL)
                winning_trades = 0
                for container in containers:
                    perf = container.get('performance', {})
                    if perf.get('pnl') is not None:
                        try:
                            pnl_str = str(perf['pnl']).replace('$', '').replace('+', '').strip()
                            if pnl_str and float(pnl_str) > 0:
                                winning_trades += 1
                        except (ValueError, TypeError):
                            pass

                stats['win_rate'] = (winning_trades / len(containers)) * 100 if containers else 0.0
            else:
                stats['win_rate'] = 0.0

        except Exception as e:
            self.logger.debug(f"Error calculating stats: {e}")

        return stats



    async def handle_stop_all_bots(self, update: Update, context) -> None:
        """Handle /stopall command for stopping all running bots"""
        try:
            # Get list of running bots first
            result = await self._execute_botsh_command([self.bot_script_path, "list"])

            if result[0] != 0:
                await self._send_error_message(update, result[2] or result[1])
                return

            # Parse containers
            all_containers = self._parse_bot_list_output(result[1])
            trading_containers = self._filter_trading_containers(all_containers)
            running_containers = [c for c in trading_containers if c.get('status') == 'running']

            if not running_containers:
                await update.message.reply_text(
                    TelegramTemplates.info_message(
                        "No Running Bots",
                        "There are no running trading bots to stop."
                    ),
                    parse_mode=ParseMode.HTML
                )
                return

            # Create confirmation message
            content = f"⚠️ {TelegramTemplates.bold('Confirm Stop All Bots')}\n\n"
            content += f"You are about to stop {len(running_containers)} running bot(s):\n\n"

            for container in running_containers:
                symbol = container.get('symbol', 'Unknown')
                content += f"• 🔴 {TelegramTemplates.bold(symbol)} ({container['name']})\n"

            content += f"\n⚠️ {TelegramTemplates.bold('Warning:')} This will stop all active trading!\n"
            content += "Open positions will remain but bots will stop monitoring them.\n\n"
            content += "Are you sure you want to continue?"

            # Create confirmation keyboard
            keyboard = [
                [
                    InlineKeyboardButton("✅ Yes, Stop All", callback_data="stopall_confirm"),
                    InlineKeyboardButton("❌ Cancel", callback_data="stopall_cancel")
                ]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(
                content,
                reply_markup=reply_markup,
                parse_mode=ParseMode.HTML
            )

        except Exception as e:
            self.logger.error(f"Error in handle_stop_all_bots: {e}")
            await self._send_error_message(update, str(e))

    async def _execute_stop_all(self, query) -> None:
        """Execute stop all bots operation"""
        try:
            # Get running containers again (in case status changed)
            result = await self._execute_botsh_command([self.bot_script_path, "list"])

            if result[0] != 0:
                await query.edit_message_text(
                    f"❌ **Error getting bot list:** {result[2] or result[1]}",
                    parse_mode=ParseMode.MARKDOWN
                )
                return

            # Parse and filter
            all_containers = self._parse_bot_list_output(result[1])
            trading_containers = self._filter_trading_containers(all_containers)
            running_containers = [c for c in trading_containers if c.get('status') == 'running']

            if not running_containers:
                await query.edit_message_text(
                    "ℹ️ **No running bots found**\n\nAll bots are already stopped.",
                    parse_mode=ParseMode.MARKDOWN
                )
                return

            # Stop each bot
            stopped_count = 0
            failed_stops = []

            status_message = f"🔄 **Stopping {len(running_containers)} bots...**\n\n"
            await query.edit_message_text(status_message, parse_mode=ParseMode.MARKDOWN)

            for container in running_containers:
                try:
                    # Use symbol instead of container name, and add --force flag
                    symbol = container.get('symbol', container['name']).replace('USDT', '').replace('usdt', '')

                    # Step 1: Stop the container
                    stop_result = await self._execute_botsh_command([
                        self.bot_script_path, "stop", symbol, "--force"
                    ])

                    if stop_result[0] == 0:
                        # Step 2: Remove the container after stopping
                        remove_result = await self._execute_botsh_command([
                            self.bot_script_path, "remove", symbol, "--force"
                        ])

                        if remove_result[0] == 0:
                            stopped_count += 1
                            status_message += f"✅ {container.get('symbol', 'Unknown')} stopped & removed\n"
                        else:
                            stopped_count += 1
                            status_message += f"⚠️ {container.get('symbol', 'Unknown')} stopped (remove failed)\n"
                    else:
                        failed_stops.append(container.get('symbol', 'Unknown'))
                        status_message += f"❌ {container.get('symbol', 'Unknown')} failed to stop\n"

                    # Update status message
                    await query.edit_message_text(status_message, parse_mode=ParseMode.MARKDOWN)

                except Exception as e:
                    failed_stops.append(container.get('symbol', 'Unknown'))
                    self.logger.error(f"Error stopping {container['name']}: {e}")

            # Final summary
            final_message = f"🏁 **Stop All Complete**\n\n"
            final_message += f"✅ Successfully stopped & removed: {stopped_count}\n"

            if failed_stops:
                final_message += f"❌ Failed to stop: {len(failed_stops)}\n"
                final_message += f"Failed bots: {', '.join(failed_stops)}\n\n"
                final_message += "💡 Try stopping failed bots individually with `/stop <symbol>`"
            else:
                final_message += "\n🎉 All trading bots have been stopped and removed successfully!"

            await query.edit_message_text(final_message, parse_mode=None)

        except Exception as e:
            self.logger.error(f"Error in _execute_stop_all: {e}")
            await query.edit_message_text(
                f"❌ Lỗi trong quá trình dừng tất cả bot: {str(e)}",
                parse_mode=None
            )
    
    async def handle_status_bot(self, update: Update, context) -> None:
        """Handle /status command for bot status"""
        if not context.args:
            # Show all bots status
            await self.handle_list_bots(update, context)
            return
        
        symbol = context.args[0].upper()
        
        try:
            result = await self._execute_botsh_command([
                self.bot_script_path, "status", symbol
            ])
            
            if result[0] == 0:
                await update.message.reply_text(
                    f"📊 **Trạng thái bot {symbol}:**\n\n```\n{result[1]}\n```",
                    parse_mode=ParseMode.MARKDOWN
                )
            else:
                await self._send_error_message(update, result[2] or result[1])
                
        except Exception as e:
            self.logger.error(f"Error getting bot status: {e}")
            await self._send_error_message(update, str(e))
    
    async def handle_logs_bot(self, update: Update, context) -> None:
        """Handle /logs command for bot logs"""
        if not context.args:
            await self._send_error_message(update, "Sử dụng: /logs <symbol> [lines]")
            return

        symbol = context.args[0].upper()
        lines = context.args[1] if len(context.args) > 1 else "50"

        try:
            # Use bot.sh logs command with correct format: logs symbol lines
            result = await self._execute_botsh_command([
                self.bot_script_path, "logs", symbol, lines
            ])

            if result[0] == 0:
                # Truncate if too long for Telegram
                output = result[1]
                if len(output) > 4000:
                    output = output[-4000:] + "\n\n... (truncated)"

                await update.message.reply_text(
                    f"📋 Logs bot {symbol} ({lines} dòng cuối):\n\n{output}",
                    parse_mode=None  # Remove markdown to avoid parsing errors
                )
            else:
                await self._send_error_message(update, result[2] or result[1])

        except Exception as e:
            self.logger.error(f"Error getting bot logs: {e}")
            await self._send_error_message(update, str(e))
    
    async def handle_restart_bot(self, update: Update, context) -> None:
        """Handle /restart command for restarting bots"""
        if not context.args:
            await self._send_error_message(update, "Sử dụng: /restart <symbol>")
            return

        symbol = context.args[0].upper()
        
        try:
            result = await self._execute_botsh_command([
                self.bot_script_path, "restart", symbol
            ])
            
            if result[0] == 0:
                await self._send_success_message(update, f"Bot {symbol} đã được khởi động lại")
            else:
                await self._send_error_message(update, result[2] or result[1])
                
        except Exception as e:
            self.logger.error(f"Error restarting bot: {e}")
            await self._send_error_message(update, str(e))

    async def handle_stop_callback(self, query, data: str) -> None:
        """Handle stop bot callback"""
        try:
            if data.startswith("stop_confirm_"):
                symbol = data.replace("stop_confirm_", "")

                # Validate symbol
                if not symbol or len(symbol.strip()) == 0:
                    await query.edit_message_text("❌ Symbol không hợp lệ")
                    return

                await self.handle_stop_confirm(query, symbol.strip())
            elif data == "stop_cancel":
                await query.edit_message_text(
                    "❌ Đã hủy dừng bot",
                    parse_mode=None
                )
            else:
                await query.edit_message_text("❌ Callback không hợp lệ")
        except Exception as e:
            self.logger.error(f"Error in handle_stop_callback: {e}")
            await query.edit_message_text(f"❌ Lỗi: {str(e)}")

    async def handle_stopall_callback(self, query, data: str) -> None:
        """Handle stop all bots callback"""
        try:
            if data == "stopall_confirm":
                await self._execute_stop_all(query)
            elif data == "stopall_cancel":
                await query.edit_message_text(
                    "❌ Đã hủy dừng tất cả bot",
                    parse_mode=None
                )
            else:
                await query.edit_message_text("❌ Callback không hợp lệ")
        except Exception as e:
            self.logger.error(f"Error in handle_stopall_callback: {e}")
            await query.edit_message_text(f"❌ Lỗi: {str(e)}")

    async def handle_list_callback(self, query) -> None:
        """Handle bot list refresh callback"""
        try:
            # Get container list using bot.sh (without --format json as it doesn't exist)
            result = await self._execute_botsh_command([
                self.bot_script_path, "list"
            ])

            if result[0] == 0:
                # Parse the output to get container data
                containers_data = self._parse_bot_list_output(result[1])

                # Use template to format the list
                from ..templates import TelegramTemplates
                template = TelegramTemplates.bot_list_enhanced(containers_data)
                keyboard = self._create_inline_keyboard(template.keyboard) if template.keyboard else None

                await query.edit_message_text(
                    template.content,
                    parse_mode=None,
                    reply_markup=keyboard
                )
            else:
                await query.edit_message_text(
                    f"❌ Không thể lấy danh sách bot: {result[2] or result[1]}",
                    parse_mode=None
                )
        except Exception as e:
            self.logger.error(f"Error in handle_list_callback: {e}")
            await query.edit_message_text(f"❌ Lỗi: {str(e)}")

    async def handle_startall_callback(self, query) -> None:
        """Handle start all bots callback"""
        try:
            # Get list of stopped containers first
            result = await self._execute_botsh_command([
                self.bot_script_path, "list", "--format", "json"
            ])

            if result[0] != 0:
                await query.edit_message_text(
                    f"❌ Không thể lấy danh sách bot: {result[2] or result[1]}",
                    parse_mode=None
                )
                return

            import json
            try:
                containers_data = json.loads(result[1]) if result[1].strip() else []
            except json.JSONDecodeError:
                containers_data = []

            stopped_containers = [c for c in containers_data if c.get('status', '').lower() != 'running']

            if not stopped_containers:
                await query.edit_message_text(
                    "ℹ️ Tất cả bot đã đang chạy",
                    parse_mode=None
                )
                return

            # Start all stopped containers
            success_count = 0
            for container in stopped_containers:
                symbol = container.get('symbol', 'unknown')
                try:
                    start_result = await self._execute_botsh_command([
                        self.bot_script_path, "start", symbol
                    ])
                    if start_result[0] == 0:
                        success_count += 1
                except Exception as e:
                    self.logger.error(f"Error starting {symbol}: {e}")

            await query.edit_message_text(
                f"✅ Đã khởi động {success_count}/{len(stopped_containers)} bot",
                parse_mode=None
            )

        except Exception as e:
            self.logger.error(f"Error in handle_startall_callback: {e}")
            await query.edit_message_text(f"❌ Lỗi: {str(e)}")

    async def handle_clean_stopped_callback(self, query) -> None:
        """Handle clean stopped bots callback"""
        try:
            # Get list of stopped containers
            result = await self._execute_botsh_command([
                self.bot_script_path, "list"
            ])

            if result[0] != 0:
                await query.edit_message_text(
                    f"❌ Không thể lấy danh sách bot: {result[2] or result[1]}",
                    parse_mode=None
                )
                return

            containers_data = self._parse_bot_list_output(result[1])
            stopped_containers = [c for c in containers_data if c.get('status', '').lower() != 'running']

            if not stopped_containers:
                await query.edit_message_text(
                    "ℹ️ Không có bot nào đang dừng để dọn dẹp",
                    parse_mode=None
                )
                return

            # Remove all stopped containers
            success_count = 0
            failed_containers = []

            for container in stopped_containers:
                container_name = container.get('name', 'unknown')
                try:
                    remove_result = await self._execute_botsh_command([
                        "docker", "rm", "-f", container_name
                    ])
                    if remove_result[0] == 0:
                        success_count += 1
                    else:
                        failed_containers.append(container_name)
                except Exception as e:
                    self.logger.error(f"Error removing {container_name}: {e}")
                    failed_containers.append(container_name)

            # Prepare response message
            if success_count > 0:
                message = f"🧹 Đã dọn dẹp {success_count} bot đã dừng"
                if failed_containers:
                    message += f"\n⚠️ Không thể xóa: {', '.join(failed_containers)}"
            else:
                message = f"❌ Không thể dọn dẹp bot nào"
                if failed_containers:
                    message += f"\nLỗi: {', '.join(failed_containers)}"

            await query.edit_message_text(
                message,
                parse_mode=None
            )

        except Exception as e:
            self.logger.error(f"Error in handle_clean_stopped_callback: {e}")
            await query.edit_message_text(f"❌ Lỗi: {str(e)}")

    def _create_inline_keyboard(self, keyboard_data):
        """Create inline keyboard from template data"""
        if not keyboard_data:
            return None

        keyboard = []
        for row in keyboard_data:
            button_row = []
            for button in row:
                button_row.append(InlineKeyboardButton(
                    text=button['text'],
                    callback_data=button['callback_data']
                ))
            keyboard.append(button_row)

        return InlineKeyboardMarkup(keyboard)
    

