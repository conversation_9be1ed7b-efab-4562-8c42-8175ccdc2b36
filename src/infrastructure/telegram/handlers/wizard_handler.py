#!/usr/bin/env python3
"""Wizard handler for Telegram bot operations."""

import asyncio
import json
import logging
import os
from typing import Dict, List, Optional, Any, Tuple

from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup, ForceReply
from telegram.constants import ParseMode

from .base_handler import BaseTelegramHandler
from ....core.credential_utils import list_profiles
from ..telegram_base import ValidationUtils


class WizardHandler(BaseTelegramHandler):
    """Handler for wizard-based operations."""

    def __init__(self, bot_token: str, chat_id: int):
        """Initialize wizard handler"""
        super().__init__(bot_token, chat_id)

    async def handle_command(self, update, context) -> None:
        """Handle command - required by abstract base class"""
        pass
    
    async def handle_createbot_wizard(self, update: Update, context) -> None:
        """Handle /createbot command - start bot creation wizard"""
        user_id = update.effective_user.id
        
        # Check prerequisites first
        if not await self._check_prerequisites():
            await self._send_safe_message(
                update.message.reply_text,
                "❌ **Không có credentials**\n\n"
                "Vui lòng thêm credentials trước với `/addcreds`",
                parse_mode=ParseMode.MARKDOWN
            )
            return
        
        # Clear any existing wizard state
        self._clear_wizard(user_id)

        # Start createbot wizard
        self._set_wizard_step(user_id, 'createbot_credentials')
        print(f"🔧 DEBUG: Started createbot wizard for user {user_id}, step: createbot_credentials")
        self.logger.info(f"🔧 Started createbot wizard for user {user_id}, step: createbot_credentials")
        
        # Get available credentials
        try:
            profiles = list_profiles()
            
            if len(profiles) == 1:
                # Only one profile, auto-select it
                profile = profiles[0]
                self._set_session_data(user_id, 'createbot_profile', profile['profile'])
                self._set_wizard_step(user_id, 'createbot_symbol')

                # Verify the state was set correctly
                verify_step = self._get_wizard_step(user_id)
                verify_profile = self._get_session_data(user_id, 'createbot_profile')
                print(f"🔧 DEBUG: Auto-selected profile {profile['profile']} for user {user_id}")
                print(f"🔧 DEBUG: Set wizard step to: createbot_symbol")
                print(f"🔧 DEBUG: Verified step: {verify_step}")
                print(f"🔧 DEBUG: Verified profile: {verify_profile}")
                print(f"🔧 DEBUG: Session manager ID: {id(self.session_manager)}")
                self.logger.info(f"🔧 Auto-selected profile {profile['profile']} for user {user_id}, step: createbot_symbol")
                
                await self._send_safe_message(
                    update.message.reply_text,
                    f"🔑 **Sử dụng profile:** {profile['profile']}\n\n"
                    "Nhập symbol để trade (ví dụ: BTC, ETH, HYPER):",
                    reply_markup=ForceReply(selective=True),
                    parse_mode=ParseMode.MARKDOWN
                )
            else:
                # Multiple profiles, let user choose
                keyboard = []
                for profile in profiles:
                    display_name = profile.get('display_name', profile['profile'])
                    keyboard.append([InlineKeyboardButton(
                        f"🔑 {display_name}",
                        callback_data=f"createbot_profile_{profile['profile']}"
                    )])
                
                reply_markup = InlineKeyboardMarkup(keyboard)
                
                await self._send_safe_message(
                    update.message.reply_text,
                    "🔑 **Chọn tài khoản để sử dụng:**",
                    reply_markup=reply_markup,
                    parse_mode=ParseMode.MARKDOWN
                )
                
        except Exception as e:
            self.logger.error(f"Error in createbot wizard: {e}")
            await self._send_error_message(update, str(e))
    
    async def handle_createbot_wizard_step(self, update: Update, context) -> None:
        """Handle steps in createbot wizard"""
        user_id = update.effective_user.id
        step = self._get_wizard_step(user_id)
        text = update.message.text.strip()
        
        if step == 'createbot_symbol':
            # Validate symbol
            if not self._validate_symbol(text):
                await self._send_error_message(update, "Symbol không hợp lệ")
                return
            
            # Normalize and save symbol
            normalized_symbol = self._normalize_symbol(text)
            self._set_session_data(user_id, 'createbot_symbol', normalized_symbol)
            self._set_wizard_step(user_id, 'createbot_amount')

            await self._send_safe_message(
                update.message.reply_text,
                f"✅ Symbol: `{normalized_symbol}`\n\n"
                "Nhập số tiền để trade (USD):\n"
                "Format: `amount dca_amount`\n"
                "Ví dụ: `50 20.5` (amount=50, dca_amount=20.5)\n\n"
                "💡 Gửi /cancel để hủy",
                reply_markup=ForceReply(selective=True),
                parse_mode=ParseMode.MARKDOWN
            )
        
        elif step == 'createbot_amount':
            # Parse amount and dca_amount from input like "50 20.5"
            parts = text.strip().split()

            if len(parts) == 1:
                # Only amount provided, use default DCA amount (50% of amount)
                try:
                    amount = float(parts[0])
                    if amount <= 0:
                        raise ValueError("Amount must be positive")
                    dca_amount = amount * 0.5  # Default DCA is 50% of amount
                except ValueError:
                    await self._send_error_message(update, "Số tiền không hợp lệ\n\n💡 Gửi /cancel để hủy")
                    return
            elif len(parts) == 2:
                # Both amount and dca_amount provided
                try:
                    amount = float(parts[0])
                    dca_amount = float(parts[1])
                    if amount <= 0 or dca_amount <= 0:
                        raise ValueError("Amounts must be positive")
                except ValueError:
                    await self._send_error_message(update, "Số tiền không hợp lệ\nFormat: `amount dca_amount`\nVí dụ: `50 20.5`\n\n💡 Gửi /cancel để hủy")
                    return
            else:
                await self._send_error_message(update, "Format không đúng\nSử dụng: `amount dca_amount`\nVí dụ: `50 20.5`\n\n💡 Gửi /cancel để hủy")
                return

            # Save amounts and defaults
            self._set_session_data(user_id, 'createbot_amount', str(amount))
            self._set_session_data(user_id, 'createbot_dca_amount', str(dca_amount))
            self._set_session_data(user_id, 'createbot_direction', 'LONG')  # Default LONG
            self._set_session_data(user_id, 'createbot_test_mode', False)   # Default LIVE mode

            # Show confirmation directly
            await self._show_createbot_confirmation(update, user_id)
        
        # Note: createbot_test_mode step removed - using default LIVE mode
    
    async def handle_createbot_profile_callback(self, query, profile: str) -> None:
        """Handle profile selection in createbot wizard"""
        user_id = query.from_user.id
        
        # Save selected profile
        self._set_session_data(user_id, 'createbot_profile', profile)
        self._set_wizard_step(user_id, 'createbot_symbol')
        self.logger.info(f"🔧 Selected profile {profile} for user {user_id}, step: createbot_symbol")
        
        await self._send_safe_message(
            query.edit_message_text,
            f"✅ **Đã chọn profile:** {profile}\n\n"
            "Nhập symbol để trade (ví dụ: BTC, ETH, HYPER):",
            parse_mode=ParseMode.MARKDOWN
        )

        # Send follow-up message with ForceReply
        await self._send_safe_message(
            query.message.reply_text,
            "Nhập symbol để trade (ví dụ: BTC, ETH, HYPER):\n\n"
            "💡 Gửi /cancel để hủy",
            reply_markup=ForceReply(selective=True)
        )
    
    async def handle_createbot_direction_callback(self, query, direction: str) -> None:
        """Handle direction selection in createbot wizard"""
        user_id = query.from_user.id
        
        # Save direction and move to test mode
        self._set_session_data(user_id, 'createbot_direction', direction)
        self._set_wizard_step(user_id, 'createbot_test_mode')
        
        # Create test mode selection keyboard
        keyboard = [
            [
                InlineKeyboardButton("🧪 Test Mode", callback_data="createbot_testmode_true"),
                InlineKeyboardButton("💰 Live Mode", callback_data="createbot_testmode_false")
            ]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await self._send_safe_message(
            query.edit_message_text,
            f"✅ **Hướng trade:** {direction}\n\n"
            "Chọn chế độ:",
            reply_markup=reply_markup,
            parse_mode=ParseMode.MARKDOWN
        )
    
    async def handle_createbot_testmode_callback(self, query, test_mode: str) -> None:
        """Handle test mode selection in createbot wizard"""
        user_id = query.from_user.id
        
        # Save test mode
        is_test = test_mode == 'true'
        self._set_session_data(user_id, 'createbot_test_mode', is_test)
        
        # Show confirmation
        await self._show_createbot_confirmation_callback(query, user_id)
    
    async def _show_createbot_confirmation(self, update: Update, user_id: int) -> None:
        """Show createbot confirmation"""
        # Get all saved data
        profile = self._get_session_data(user_id, 'createbot_profile')
        symbol = self._get_session_data(user_id, 'createbot_symbol')
        amount = self._get_session_data(user_id, 'createbot_amount')
        dca_amount = self._get_session_data(user_id, 'createbot_dca_amount')
        direction = self._get_session_data(user_id, 'createbot_direction', 'LONG')
        test_mode = self._get_session_data(user_id, 'createbot_test_mode', False)

        # Create confirmation message
        mode_text = "🧪 Test Mode" if test_mode else "💰 Live Mode"
        message = (
            "🤖 Xác nhận tạo bot\n\n"
            f"🔑 Profile: {profile}\n"
            f"📊 Symbol: {symbol}\n"
            f"💰 Amount: ${amount}\n"
            f"📊 DCA Amount: ${dca_amount}\n"
            f"📈 Direction: {direction}\n"
            f"⚙️ Mode: {mode_text}\n\n"
            "Xác nhận tạo bot?"
        )
        
        # Create confirmation keyboard
        keyboard = [
            [
                InlineKeyboardButton("✅ Tạo Bot", callback_data="createbot_confirm"),
                InlineKeyboardButton("❌ Hủy", callback_data="createbot_cancel")
            ]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await self._send_safe_message(
            update.message.reply_text,
            message,
            reply_markup=reply_markup,
            parse_mode=None  # Remove markdown to avoid parsing errors
        )
    
    async def _show_createbot_confirmation_callback(self, query, user_id: int) -> None:
        """Show createbot confirmation via callback"""
        # Get all saved data
        profile = self._get_session_data(user_id, 'createbot_profile')
        symbol = self._get_session_data(user_id, 'createbot_symbol')
        amount = self._get_session_data(user_id, 'createbot_amount')
        dca_amount = self._get_session_data(user_id, 'createbot_dca_amount')
        direction = self._get_session_data(user_id, 'createbot_direction', 'LONG')
        test_mode = self._get_session_data(user_id, 'createbot_test_mode', False)

        # Create confirmation message
        mode_text = "🧪 Test Mode" if test_mode else "💰 Live Mode"
        message = (
            "🤖 **Xác nhận tạo bot**\n\n"
            f"🔑 **Profile:** {profile}\n"
            f"📊 **Symbol:** {symbol}\n"
            f"💰 **Amount:** ${amount}\n"
            f"📊 **DCA Amount:** ${dca_amount}\n"
            f"📈 **Direction:** {direction}\n"
            f"⚙️ **Mode:** {mode_text}\n\n"
            "Xác nhận tạo bot?"
        )
        
        # Create confirmation keyboard
        keyboard = [
            [
                InlineKeyboardButton("✅ Tạo Bot", callback_data="createbot_confirm"),
                InlineKeyboardButton("❌ Hủy", callback_data="createbot_cancel")
            ]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await self._send_safe_message(
            query.edit_message_text,
            message,
            reply_markup=reply_markup,
            parse_mode=None  # Remove markdown to avoid parsing errors
        )
    
    async def handle_createbot_confirm(self, query) -> None:
        """Handle createbot confirmation"""
        user_id = query.from_user.id
        
        try:
            # Get all saved data
            profile = self._get_session_data(user_id, 'createbot_profile')
            symbol = self._get_session_data(user_id, 'createbot_symbol')
            amount = self._get_session_data(user_id, 'createbot_amount')
            dca_amount = self._get_session_data(user_id, 'createbot_dca_amount')
            direction = self._get_session_data(user_id, 'createbot_direction', 'LONG')
            test_mode = self._get_session_data(user_id, 'createbot_test_mode', False)

            # Cleanup existing container first (consistent with bot.sh behavior)
            await self._cleanup_existing_container(symbol)

            # Create bot using Python Docker library instead of bot.sh
            result = await self._create_trading_bot_container(
                symbol=symbol,
                amount=amount,
                dca_amount=dca_amount,
                profile=profile,
                direction=direction,
                test_mode=test_mode
            )
            
            if result[0] == 0:
                mode_text = "🧪 Test Mode" if test_mode else "💰 Live Mode"
                await self._send_safe_message(
                    query.edit_message_text,
                    f"🚀 **Bot đã được tạo thành công!**\n\n"
                    f"📊 **Symbol:** {symbol}\n"
                    f"💰 **Amount:** ${amount}\n"
                    f"📊 **DCA Amount:** ${dca_amount}\n"
                    f"📈 **Direction:** {direction}\n"
                    f"⚙️ **Mode:** {mode_text}\n\n"
                    f"Sử dụng `/status {symbol}` để xem trạng thái.",
                    parse_mode=ParseMode.MARKDOWN
                )
            else:
                # Cleanup failed container if it exists
                await self._cleanup_failed_container(symbol)

                await self._send_safe_message(
                    query.edit_message_text,
                    f"❌ **Lỗi tạo bot:**\n```\n{result[2] or result[1]}\n```\n\n"
                    f"Container đã được dọn dẹp. Thử lại với `/createbot`.",
                    parse_mode=ParseMode.MARKDOWN
                )
            
            # Clear wizard state
            self._clear_wizard(user_id)
            
        except Exception as e:
            self.logger.error(f"Error creating bot: {e}")

            # Cleanup failed container if it exists
            symbol = self._get_session_data(user_id, 'createbot_symbol')
            if symbol:
                await self._cleanup_failed_container(symbol)

            await query.edit_message_text(
                f"❌ **Lỗi:** {str(e)}\n\n"
                f"Container đã được dọn dẹp. Thử lại với `/createbot`.",
                parse_mode=ParseMode.MARKDOWN
            )
            self._clear_wizard(user_id)
    
    async def handle_cancel(self, update: Update, context) -> None:
        """Handle /cancel command"""
        user_id = update.effective_user.id

        if self._is_in_wizard(user_id):
            self._clear_wizard(user_id)
            await self._send_success_message(update, "Đã hủy thao tác hiện tại")
        else:
            await self._send_info_message(update, "Không có thao tác nào để hủy")

    async def handle_createbot_callback(self, query, data: str) -> None:
        """Handle createbot callback"""
        try:
            user_id = query.from_user.id

            if data.startswith("createbot_profile_"):
                profile = data.replace("createbot_profile_", "")

                # Security: Validate profile name thoroughly
                if not profile or not self._validate_profile_name(profile):
                    await query.edit_message_text("❌ Profile name không hợp lệ hoặc không an toàn")
                    return

                # Security: Sanitize profile name
                sanitized_profile = self._sanitize_input(profile.strip())

                # Set selected profile and move to symbol step
                self._set_session_data(user_id, 'createbot_profile', sanitized_profile)
                self._set_wizard_step(user_id, 'createbot_symbol')

                await self._send_safe_message(
                    query.edit_message_text,
                    f"🔑 Sử dụng profile: {profile}\n\n"
                    "Nhập symbol để trade (ví dụ: BTC, ETH, SOL):",
                    parse_mode=None
                )
            elif data == "createbot_confirm":
                await self._execute_createbot(query, user_id)
            elif data == "createbot_cancel":
                self._clear_wizard(user_id)
                await query.edit_message_text(
                    "❌ Đã hủy tạo bot",
                    parse_mode=None
                )
            else:
                await query.edit_message_text("❌ Callback không hợp lệ")
        except Exception as e:
            self.logger.error(f"Error in handle_createbot_callback: {e}")
            await query.edit_message_text(f"❌ Lỗi: {str(e)}")

    async def _cleanup_existing_container(self, symbol: str) -> None:
        """Cleanup existing container before creating new one (consistent with bot.sh)"""
        try:
            container_name = self._get_container_name(symbol)

            # Check if container exists using docker command (like bot.sh does)
            check_cmd = ["docker", "ps", "-a", "--format", "{{.Names}}"]
            result = await self._execute_docker_cmd(check_cmd)

            if result[0] == 0 and container_name in result[1]:
                self.logger.info(f"Container '{container_name}' exists - cleaning up before creating new one")

                # Stop container
                stop_cmd = ["docker", "stop", container_name]
                await self._execute_docker_cmd(stop_cmd)
                self.logger.info(f"Stopped container '{container_name}'")

                # Remove container
                rm_cmd = ["docker", "rm", container_name]
                await self._execute_docker_cmd(rm_cmd)
                self.logger.info(f"Removed container '{container_name}'")
            else:
                self.logger.info(f"No existing container '{container_name}' found - proceeding with creation")

        except Exception as e:
            self.logger.error(f"Error during existing container cleanup: {e}")
            # Log the error but don't fail the entire process if cleanup fails
            # This is expected behavior as the container might not exist

    async def _cleanup_failed_container(self, symbol: str) -> None:
        """Cleanup failed container after bot creation error"""
        try:
            container_name = self._get_container_name(symbol)
            self.logger.info(f"Cleaning up failed container: {container_name}")

            # Check if container exists using docker command
            check_cmd = ["docker", "ps", "-a", "--format", "{{.Names}}"]
            result = await self._execute_docker_cmd(check_cmd)

            if result[0] == 0 and container_name in result[1]:
                # Stop container
                stop_cmd = ["docker", "stop", container_name]
                await self._execute_docker_cmd(stop_cmd)
                self.logger.info(f"Stopped failed container: {container_name}")

                # Remove container
                rm_cmd = ["docker", "rm", container_name]
                await self._execute_docker_cmd(rm_cmd)
                self.logger.info(f"Removed failed container: {container_name}")
            else:
                self.logger.info(f"Failed container '{container_name}' not found - already cleaned up")

        except Exception as e:
            self.logger.error(f"Error during failed container cleanup: {e}")
            # Log cleanup errors but don't propagate them as they're not critical

    async def _create_trading_bot_container(self, symbol: str, amount: float, dca_amount: float,
                                          profile: str, direction: str, test_mode: bool) -> tuple:
        """Create trading bot container using docker command (like bot.sh does)"""
        try:
            import json
            import os

            container_name = self._get_container_name(symbol)

            # Get credentials for the profile
            credentials = await self._get_profile_credentials(profile)
            if not credentials:
                return (1, "", f"No credentials found for profile: {profile}")

            # Prepare symbol (ensure USDT suffix)
            full_symbol = self._normalize_symbol(symbol)

            # Create config file content
            config_content = await self._create_bot_config(
                symbol=full_symbol,
                amount=amount,
                dca_amount=dca_amount,
                direction=direction,
                test_mode=test_mode
            )

            # Write config file (use container path for writing, but host path for volume mount)
            config_path = f"/app/configs/{container_name}.json"
            with open(config_path, 'w') as f:
                json.dump(config_content, f, indent=2)

            # Docker image - use existing autotrader image
            trader_image = "autotrader:latest"

            # Get host paths for Docker in Docker (like bot.sh does)
            host_config_dir = os.environ.get('HOST_CONFIG_DIR', '/app/configs')
            host_data_dir = os.environ.get('HOST_DATA_DIR', '/app/data')
            host_logs_dir = os.environ.get('HOST_LOGS_DIR', '/app/logs')

            # Build docker run command (similar to bot.sh)
            # Use standard BYBIT_API_KEY and BYBIT_API_SECRET (not profile-specific names)
            docker_cmd = [
                "docker", "run", "-d",
                "--name", container_name,
                "--restart", "unless-stopped",
                "-e", f"BYBIT_API_KEY={credentials['api_key']}",
                "-e", f"BYBIT_API_SECRET={credentials['api_secret']}",
                "-e", f"BOT_CONFIG_FILE=configs/{container_name}.json",
                "-e", f"TRADE_SYMBOL={full_symbol}",
                "-e", f"TRADE_AMOUNT={amount}",
                "-e", f"TRADE_DIRECTION={direction}",
                "-e", f"TEST_MODE={str(test_mode).lower()}",
                "-v", f"{host_config_dir}:/app/configs",
                "-v", f"{host_data_dir}:/app/data",
                "-v", f"{host_logs_dir}:/app/logs",
                trader_image,
                "python", "main.py", "--start"
            ]

            if test_mode:
                docker_cmd.append("--test")

            # Execute docker run command
            result = await self._execute_docker_cmd(docker_cmd)

            if result[0] == 0:
                container_id = result[1].strip()
                self.logger.info(f"Created trading bot container: {container_id[:12]}")
                return (0, f"Container {container_name} created successfully", "")
            else:
                error_msg = result[2] or result[1]
                self.logger.error(f"Failed to create container: {error_msg}")
                return (1, "", error_msg)

        except Exception as e:
            self.logger.error(f"Error creating trading bot container: {e}")
            return (1, "", str(e))

    async def _execute_docker_cmd(self, cmd: List[str]) -> Tuple[int, str, str]:
        """Execute docker command with security validation"""
        try:
            # Security: Validate command structure
            if not cmd or len(cmd) == 0:
                raise ValueError("Empty command not allowed")

            # Security: Only allow docker commands
            if cmd[0] != "docker":
                raise ValueError(f"Only docker commands allowed, got: {cmd[0]}")

            # Security: Validate all command arguments
            for arg in cmd:
                if not isinstance(arg, str):
                    raise ValueError(f"Invalid argument type: {type(arg)}")

                # Check for shell metacharacters in arguments
                if any(c in ValidationUtils.SHELL_METACHARACTERS for c in arg):
                    raise ValueError(f"Dangerous characters in argument: {arg}")

                # Limit argument length
                if len(arg) > 200:
                    raise ValueError(f"Argument too long: {len(arg)} chars")

            # Security: Log the command for audit
            safe_cmd = ' '.join(cmd)
            self.logger.info(f"Executing docker command: {safe_cmd}")

            # Run docker command asynchronously
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd="/app"
            )

            stdout, stderr = await process.communicate()

            return (
                process.returncode,
                stdout.decode('utf-8', errors='ignore').strip(),
                stderr.decode('utf-8', errors='ignore').strip()
            )

        except Exception as e:
            self.logger.error(f"Error executing docker command {' '.join(cmd) if cmd else 'None'}: {e}")
            return 1, "", str(e)

    async def _get_profile_credentials(self, profile: str) -> dict:
        """Get credentials for a profile"""
        try:
            import json

            # Read credentials directly from JSON files
            credentials_dir = "/root/.autotrader/credentials"
            profile_file = f"{credentials_dir}/{profile}.json"

            if not os.path.exists(profile_file):
                self.logger.error(f"Credentials file not found: {profile_file}")
                return None

            with open(profile_file, 'r') as f:
                credentials_data = json.load(f)

            # Extract API key and secret from JSON
            if 'api_key' in credentials_data and 'api_secret' in credentials_data:
                return {
                    'api_key': credentials_data['api_key'],
                    'api_secret': credentials_data['api_secret']
                }
            else:
                self.logger.error(f"Incomplete credentials in {profile_file}")
                return None

        except Exception as e:
            self.logger.error(f"Error getting credentials for profile {profile}: {e}")
            return None

    async def _create_bot_config(self, symbol: str, amount: float, dca_amount: float,
                                direction: str, test_mode: bool) -> dict:
        """Create bot configuration"""
        try:
            # Load template config
            template_path = "/app/configs/template.json"
            if os.path.exists(template_path):
                with open(template_path, 'r') as f:
                    config = json.load(f)
            else:
                # Create basic config if template doesn't exist
                config = {
                    "symbol": symbol,
                    "exchange": "bybit",
                    "direction": direction,
                    "amount": amount,
                    "use_test_mode": test_mode,
                    "dca": {
                        "enabled": True,
                        "strategies": {
                            "BB_LOWER": {
                                "amount": dca_amount,
                                "enabled": True
                            }
                        }
                    }
                }

            # Update config with provided values (similar to bot.sh Python script)
            config["symbol"] = symbol
            config["direction"] = direction
            config["amount"] = float(amount)
            config["use_test_mode"] = test_mode
            config["use_sandbox"] = test_mode

            # Update DCA amount if provided (similar to bot.sh: config['dca_amount'] = float('$dca_amount'))
            if dca_amount > 0:
                config["dca_amount"] = float(dca_amount)

            return config

        except Exception as e:
            self.logger.error(f"Error creating bot config: {e}")
            # Return minimal config
            return {
                "symbol": symbol,
                "exchange": "bybit",
                "direction": direction,
                "amount": amount,
                "use_test_mode": test_mode
            }
