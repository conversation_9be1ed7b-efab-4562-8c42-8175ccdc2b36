#!/usr/bin/env python3
"""Base handler for Telegram bot operations."""

import asyncio
import logging
import os
from typing import Dict, List, Optional, Any, Tuple

from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup, ForceReply
from telegram.ext import Application, CommandHandler, MessageHandler, CallbackQueryHandler, filters
from telegram.constants import ParseMode

from ..telegram_base import <PERSON><PERSON>ramBase<PERSON>and<PERSON>, UserSessionManager, ValidationUtils
from ..templates import TelegramTemplates
from ..trading_notifications import TradingNotificationManager
from ..safe_message_sender import SafeMessageSender

# Import centralized utilities
from ....core.constants import CREDENTIALS_DIR, ensure_directories
from ....core.credential_utils import list_profiles, load_credentials, store_credentials


class BaseTelegramHandler(TelegramBaseHandler):
    """Base class for all Telegram handlers with common functionality."""

    def __init__(self, bot_token: str, chat_id: int):
        super().__init__('BaseTelegramHandler')
        self.bot_token = bot_token
        self.chat_id = chat_id
        self.application = None
        self.session_manager = UserSessionManager()
        self.bot_script_path = "/app/bot.sh"  # Path to bot.sh script

        # Initialize notification manager
        self.notification_manager = TradingNotificationManager(bot_token, chat_id)
        self.safe_sender = SafeMessageSender(self.logger)

    async def handle_command(self, update, context) -> None:
        """Handle command - required by abstract base class"""
        pass

    def _create_inline_keyboard(self, keyboard_data: List[List[Dict[str, str]]]) -> InlineKeyboardMarkup:
        """Create inline keyboard from template data"""
        keyboard = []
        for row in keyboard_data:
            button_row = []
            for button_data in row:
                button = InlineKeyboardButton(
                    text=button_data["text"],
                    callback_data=button_data["callback_data"]
                )
                button_row.append(button)
            keyboard.append(button_row)
        return InlineKeyboardMarkup(keyboard)

    async def _execute_botsh_command(self, command: List[str]) -> Tuple[int, str, str]:
        """Execute bot.sh command and return (exit_code, stdout, stderr)"""
        try:
            # Check if this is a direct docker command and handle it specially
            if len(command) > 0 and command[0] == "docker":
                return await self._execute_docker_command(command[1:])

            # Set environment variables for container execution
            # This tells bot.sh that it's running inside a container and provides host paths
            # The host paths are the original paths that were mounted into this Telegram container
            env = {
                **os.environ,
                'RUNNING_IN_CONTAINER': 'true',
                # Get the original host paths from environment variables set during container startup
                # These should be the actual host directories that were mounted
                'HOST_CONFIG_DIR': os.environ.get('HOST_CONFIG_DIR', '/app/configs'),
                'HOST_DATA_DIR': os.environ.get('HOST_DATA_DIR', '/app/data'),
                'HOST_LOGS_DIR': os.environ.get('HOST_LOGS_DIR', '/app/logs'),
                # Add docker socket path
                'DOCKER_HOST': 'unix:///var/run/docker.sock'
            }

            # Run command asynchronously
            process = await asyncio.create_subprocess_exec(
                *command,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd="/app",
                env=env
            )

            stdout, stderr = await process.communicate()

            return (
                process.returncode,
                stdout.decode('utf-8', errors='ignore').strip(),
                stderr.decode('utf-8', errors='ignore').strip()
            )

        except Exception as e:
            self.logger.error(f"Error executing command {' '.join(command)}: {e}")
            return 1, "", str(e)

    async def _execute_docker_command(self, docker_args: List[str]) -> Tuple[int, str, str]:
        """Execute docker command using docker socket"""
        try:
            import docker
            # Use explicit socket path for container environment
            client = docker.DockerClient(base_url='unix://var/run/docker.sock')

            # Handle different docker commands
            if len(docker_args) == 0:
                return 1, "", "No docker command specified"

            cmd = docker_args[0]

            if cmd == "ps":
                # List containers
                containers = client.containers.list(all=True)
                if "-a" in docker_args and "--format" in docker_args:
                    # Format as names only
                    names = [c.name for c in containers]
                    return 0, "\n".join(names), ""
                else:
                    # Default ps format
                    output = []
                    for c in containers:
                        status = c.status
                        output.append(f"{c.id[:12]} {c.name} {status}")
                    return 0, "\n".join(output), ""

            elif cmd == "stop":
                # Stop container
                if len(docker_args) < 2:
                    return 1, "", "Container name required"
                container_name = docker_args[1]
                try:
                    container = client.containers.get(container_name)
                    container.stop()
                    return 0, f"Stopped {container_name}", ""
                except docker.errors.NotFound:
                    return 1, "", f"Container {container_name} not found"

            elif cmd == "rm":
                # Remove container
                if len(docker_args) < 2:
                    return 1, "", "Container name required"
                container_name = docker_args[1]
                try:
                    container = client.containers.get(container_name)
                    container.remove()
                    return 0, f"Removed {container_name}", ""
                except docker.errors.NotFound:
                    return 1, "", f"Container {container_name} not found"

            elif cmd == "run":
                # This is complex, let's delegate back to bot.sh
                # But first ensure we have docker binary available
                return await self._execute_docker_via_socket(docker_args)

            else:
                return 1, "", f"Unsupported docker command: {cmd}"

        except ImportError:
            # Docker library not available, try alternative approach
            return await self._execute_docker_via_socket(docker_args)
        except Exception as e:
            self.logger.error(f"Error executing docker command: {e}")
            return 1, "", str(e)

    async def _execute_docker_via_socket(self, docker_args: List[str]) -> Tuple[int, str, str]:
        """Execute docker command via socket using curl/HTTP API"""
        try:
            # For now, return error - we'll implement this if needed
            return 1, "", "Docker command execution in container not fully implemented yet"
        except Exception as e:
            return 1, "", str(e)

    def _parse_credentials_list(self, output: str) -> List[Dict[str, str]]:
        """Parse credentials list from bot.sh output"""
        credentials = []
        lines = output.split('\n')
        
        current_profile = None
        current_display_name = None
        
        for line in lines:
            line = line.strip()
            
            # New format: 🔑 profile_name
            if line.startswith('🔑 '):
                current_profile = line[2:].strip()
                current_display_name = current_profile  # Default to profile name
                
            # Extract display name: Display Name: actual_name
            elif line.startswith('Display Name:') and current_profile:
                current_display_name = line.split(':', 1)[1].strip()
                
            # When we hit Format: or Total:, save the current credential
            elif (line.startswith('Format:') or line.startswith('📊 Total:')) and current_profile:
                credentials.append({
                    'profile': current_profile,
                    'display_name': current_display_name or current_profile
                })
                current_profile = None
                current_display_name = None
        
        # Handle case where last credential doesn't have Format line
        if current_profile:
            credentials.append({
                'profile': current_profile,
                'display_name': current_display_name or current_profile
            })

        return credentials
    
    async def _check_prerequisites(self) -> bool:
        """Check if prerequisites (credentials) are available"""
        try:
            # Use centralized credential utilities
            profiles = list_profiles()
            return len(profiles) > 0
            
        except Exception as e:
            self.logger.error(f"Prerequisites check failed with exception: {e}")
            return False
    
    def _create_inline_keyboard(self, buttons: List[List[Dict[str, str]]]) -> InlineKeyboardMarkup:
        """Create inline keyboard from button configuration"""
        keyboard = []
        for row in buttons:
            keyboard_row = []
            for button in row:
                keyboard_row.append(InlineKeyboardButton(
                    text=button['text'],
                    callback_data=button['callback_data']
                ))
            keyboard.append(keyboard_row)
        return InlineKeyboardMarkup(keyboard)

    async def _send_safe_message(self, send_func, text: str, parse_mode=None, **kwargs):
        """Send message with automatic fallback for parsing errors"""
        return await self.safe_sender.send_safe_message(send_func, text, parse_mode, **kwargs)

    async def _send_error_message(self, update: Update, error_msg: str):
        """Send formatted error message with safe parsing"""
        await self._send_safe_message(
            update.message.reply_text,
            f"❌ **Lỗi:** {error_msg}",
            parse_mode=ParseMode.MARKDOWN
        )
    
    async def _send_success_message(self, update: Update, success_msg: str):
        """Send formatted success message with safe parsing"""
        await self._send_safe_message(
            update.message.reply_text,
            f"✅ **Thành công:** {success_msg}",
            parse_mode=ParseMode.MARKDOWN
        )

    async def _send_info_message(self, update: Update, info_msg: str):
        """Send formatted info message with safe parsing"""
        await self._send_safe_message(
            update.message.reply_text,
            f"ℹ️ **Thông tin:** {info_msg}",
            parse_mode=ParseMode.MARKDOWN
        )
    
    def _validate_symbol(self, symbol: str) -> bool:
        """Validate trading symbol with security checks"""
        return ValidationUtils.validate_symbol(symbol)

    def _validate_amount(self, amount: str) -> bool:
        """Validate trading amount with security checks"""
        return ValidationUtils.validate_amount(amount)

    def _validate_api_key(self, api_key: str) -> bool:
        """Validate API key with security checks"""
        return ValidationUtils.validate_api_key(api_key)

    def _validate_profile_name(self, profile: str) -> bool:
        """Validate profile name with security checks"""
        return ValidationUtils.validate_profile_name(profile)

    def _sanitize_input(self, input_str: str) -> str:
        """Sanitize user input to prevent injection attacks"""
        return ValidationUtils.sanitize_input(input_str)

    def _normalize_symbol(self, symbol: str) -> str:
        """Normalize symbol to full format with security validation"""
        # Security: Validate and sanitize input first
        if not self._validate_symbol(symbol):
            raise ValueError(f"Invalid symbol: {symbol}")

        sanitized_symbol = self._sanitize_input(symbol)

        if "/" not in sanitized_symbol:
            # Simple symbol like "btc" -> "BTC/USDT:USDT"
            return f"{sanitized_symbol.upper()}/USDT:USDT"
        return sanitized_symbol

    def _get_container_name(self, symbol: str) -> str:
        """Generate container name from symbol with security validation"""
        # Security: Validate symbol first
        if not self._validate_symbol(symbol):
            raise ValueError(f"Invalid symbol for container name: {symbol}")

        # Extract base symbol (e.g., ETH/USDT:USDT -> eth, BTC/USDT:USDT -> btc)
        if "/" in symbol:
            # Full format like "ETH/USDT:USDT" -> "eth"
            base_symbol = symbol.split("/")[0]
        elif symbol.upper().endswith("USDT") and len(symbol) > 4:
            # Format like "ETHUSDT" -> "eth"
            base_symbol = symbol[:-4]
        else:
            # Simple format like "ETH" or "eth" -> "eth"
            base_symbol = symbol

        # Convert to lowercase and sanitize
        base_symbol = self._sanitize_input(base_symbol.lower())

        # Remove any existing 'usdt' suffix to avoid duplication
        if base_symbol.lower().endswith('usdt'):
            base_symbol = base_symbol[:-4]

        # Add 'usdt' suffix for clarity - consistent with shell_constants.sh
        container_name = f"{base_symbol}usdt"

        # Security: Final validation of container name
        if not ValidationUtils.validate_container_name(container_name):
            raise ValueError(f"Invalid container name generated: {container_name}")

        return container_name
    
    async def _get_active_containers(self) -> List[Dict[str, Any]]:
        """Get list of active trading containers"""
        try:
            result = await self._execute_botsh_command([self.bot_script_path, "list"])
            if result[0] == 0:
                # Parse container list from output
                containers = []
                lines = result[1].split('\n')
                for line in lines:
                    if '🤖' in line and 'Running' in line:
                        # Extract container info
                        parts = line.split()
                        if len(parts) >= 2:
                            containers.append({
                                'name': parts[1],
                                'status': 'Running',
                                'symbol': parts[1].upper()
                            })
                return containers
            return []
        except Exception as e:
            self.logger.error(f"Error getting active containers: {e}")
            return []
    
    async def _format_container_status(self, containers: List[Dict[str, Any]]) -> str:
        """Format container status for display"""
        if not containers:
            return "📭 **Không có bot nào đang chạy**"
        
        message = "🤖 **Bot đang hoạt động:**\n\n"
        for container in containers:
            message += f"• **{container['symbol']}** - {container['status']}\n"
        
        return message
    
    def _get_session_data(self, user_id: int, key: str, default=None):
        """Get data from user session"""
        return self.session_manager.get_session_data(user_id, key, default)
    
    def _set_session_data(self, user_id: int, key: str, value):
        """Set data in user session"""
        self.session_manager.set_session_data(user_id, key, value)
    
    def _clear_session(self, user_id: int):
        """Clear user session"""
        self.session_manager.clear_session(user_id)
    
    def _is_in_wizard(self, user_id: int) -> bool:
        """Check if user is in a wizard"""
        return self.session_manager.is_in_wizard(user_id)
    
    def _get_wizard_step(self, user_id: int) -> Optional[str]:
        """Get current wizard step"""
        return self.session_manager.get_wizard_step(user_id)
    
    def _set_wizard_step(self, user_id: int, step: str):
        """Set wizard step"""
        self.session_manager.set_wizard_step(user_id, step)
    
    def _clear_wizard(self, user_id: int):
        """Clear wizard state"""
        self.session_manager.clear_wizard(user_id)
