#!/bin/bash
# Shell constants for AutoTrader application
# Centralized configuration to avoid code duplication in shell scripts
# Source this file in shell scripts: source src/core/shell_constants.sh

# ===============================
# Directory Constants
# ===============================

# Main application directories
AUTOTRADER_DIR="$HOME/.autotrader"
CREDENTIALS_DIR="$AUTOTRADER_DIR/credentials"
CONFIG_DIR="./configs"
DATA_DIR="./data"
LOGS_DIR="./logs"

# ===============================
# Docker Constants
# ===============================

# Docker image names
TELEGRAM_IMAGE="${TELEGRAM_IMAGE:-autotrader-telegram:latest}"
TRADER_IMAGE="${TRADER_IMAGE:-autotrader-trader:latest}"

# ===============================
# Environment Variable Constants
# ===============================

# Telegram environment variables
TELEGRAM_BOT_TOKEN_VAR="TELEGRAM_BOT_TOKEN"
TELEGRAM_CHAT_ID_VAR="TELEGRAM_CHAT_ID"

# Bybit API environment variables
BYBIT_API_KEY_VAR="BYBIT_API_KEY"
BYBIT_API_SECRET_VAR="BYBIT_API_SECRET"
BYBIT_SECRET_KEY_VAR="BYBIT_SECRET_KEY"  # Alternative name

# ===============================
# Default Values
# ===============================

# Default trading configuration
DEFAULT_TRADING_AMOUNT="50"
DEFAULT_SYMBOL_SUFFIX="/USDT:USDT"
DEFAULT_DIRECTION="LONG"
DEFAULT_TEST_MODE="false"

# Default credential profile
DEFAULT_CREDENTIAL_PROFILE="default"

# ===============================
# File Permissions
# ===============================

# Secure file permissions for credentials
CREDENTIAL_FILE_PERMISSIONS="600"

# ===============================
# Utility Functions
# ===============================

# Ensure required directories exist
ensure_directories() {
    mkdir -p "$AUTOTRADER_DIR" "$CREDENTIALS_DIR" "$CONFIG_DIR" "$DATA_DIR" "$LOGS_DIR"
    chmod 755 "$AUTOTRADER_DIR" "$CREDENTIALS_DIR" 2>/dev/null || true
}

# Normalize symbol to full format
normalize_symbol() {
    local symbol="$1"
    local upper_symbol=$(echo "$symbol" | tr '[:lower:]' '[:upper:]')

    # If already contains slash, return as-is
    if [[ "$upper_symbol" =~ / ]]; then
        echo "$upper_symbol"
        return
    fi

    # Handle symbols that already contain USDT but no slash
    if [[ "$upper_symbol" =~ USDT$ ]]; then
        # Extract base symbol by removing USDT suffix
        local base_symbol="${upper_symbol%USDT}"
        echo "${base_symbol}/USDT:USDT"
    else
        # Simple symbol like "BTC" -> "BTC/USDT:USDT"
        echo "${upper_symbol}$DEFAULT_SYMBOL_SUFFIX"
    fi
}

# Generate container name from symbol
get_container_name() {
    local symbol="$1"
    # Extract base symbol (e.g., ETH/USDT:USDT -> eth, BTC/USDT:USDT -> btc)
    local base_symbol=$(echo "$symbol" | cut -d'/' -f1 | tr '[:upper:]' '[:lower:]')

    # Remove any existing 'usdt' suffix to avoid duplication
    base_symbol="${base_symbol%usdt}"

    # Add 'usdt' suffix for clarity
    echo "${base_symbol}usdt"
}

# Get credential file path
get_credential_file_path() {
    local profile="$1"
    local format="${2:-json}"
    
    case "$format" in
        "json")
            echo "$CREDENTIALS_DIR/${profile}.json"
            ;;
        "env")
            echo "$CREDENTIALS_DIR/${profile}.env"
            ;;
        "display")
            echo "$CREDENTIALS_DIR/${profile}.display"
            ;;
        *)
            echo "❌ Unknown credential format: $format" >&2
            return 1
            ;;
    esac
}

# Get environment variable with default value
get_env_var() {
    local var_name="$1"
    local default_value="${2:-}"
    echo "${!var_name:-$default_value}"
}

# Check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Print colored output
print_success() {
    echo "✅ $*"
}

print_error() {
    echo "❌ $*" >&2
}

print_warning() {
    echo "⚠️ $*"
}

print_info() {
    echo "ℹ️ $*"
}

# Docker volume mounts (for consistency)
get_docker_volume_mounts() {
    echo "-v $CONFIG_DIR:/app/configs"
    echo "-v $DATA_DIR:/app/data"
    echo "-v $LOGS_DIR:/app/logs"
    echo "-v $CREDENTIALS_DIR:/root/.autotrader/credentials"
    echo "-v /var/run/docker.sock:/var/run/docker.sock"
}

# Get Docker environment variables for telegram bot
get_telegram_docker_env() {
    local token=$(get_env_var "$TELEGRAM_BOT_TOKEN_VAR")
    local chat_id=$(get_env_var "$TELEGRAM_CHAT_ID_VAR")
    
    if [[ -n "$token" && -n "$chat_id" ]]; then
        echo "-e $TELEGRAM_BOT_TOKEN_VAR=$token"
        echo "-e $TELEGRAM_CHAT_ID_VAR=$chat_id"
        return 0
    else
        return 1
    fi
}

# Get Docker environment variables for trader bot
get_trader_docker_env() {
    local api_key=$(get_env_var "$BYBIT_API_KEY_VAR")
    local api_secret=$(get_env_var "$BYBIT_API_SECRET_VAR")
    
    if [[ -n "$api_key" && -n "$api_secret" ]]; then
        echo "-e $BYBIT_API_KEY_VAR=$api_key"
        echo "-e $BYBIT_API_SECRET_VAR=$api_secret"
        return 0
    else
        return 1
    fi
}

# Validate required environment variables
validate_telegram_env() {
    local token=$(get_env_var "$TELEGRAM_BOT_TOKEN_VAR")
    local chat_id=$(get_env_var "$TELEGRAM_CHAT_ID_VAR")
    
    if [[ -z "$token" ]]; then
        print_error "TELEGRAM_BOT_TOKEN is required"
        return 1
    fi
    
    if [[ -z "$chat_id" ]]; then
        print_error "TELEGRAM_CHAT_ID is required"
        return 1
    fi
    
    return 0
}

validate_trader_env() {
    local api_key=$(get_env_var "$BYBIT_API_KEY_VAR")
    local api_secret=$(get_env_var "$BYBIT_API_SECRET_VAR")
    
    if [[ -z "$api_key" ]]; then
        print_error "BYBIT_API_KEY is required"
        return 1
    fi
    
    if [[ -z "$api_secret" ]]; then
        print_error "BYBIT_API_SECRET is required"
        return 1
    fi
    
    return 0
}

# Export constants for use in other scripts
export AUTOTRADER_DIR CREDENTIALS_DIR CONFIG_DIR DATA_DIR LOGS_DIR
export TELEGRAM_IMAGE TRADER_IMAGE
export TELEGRAM_BOT_TOKEN_VAR TELEGRAM_CHAT_ID_VAR
export BYBIT_API_KEY_VAR BYBIT_API_SECRET_VAR BYBIT_SECRET_KEY_VAR
export DEFAULT_TRADING_AMOUNT DEFAULT_SYMBOL_SUFFIX DEFAULT_DIRECTION DEFAULT_TEST_MODE
export DEFAULT_CREDENTIAL_PROFILE CREDENTIAL_FILE_PERMISSIONS
