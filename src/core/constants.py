"""
Core constants for AutoTrader application
Centralized configuration to avoid code duplication
"""

from pathlib import Path
import os

# ===============================
# Directory Constants
# ===============================

# Home directory
HOME_DIR = Path.home()

# Main application directories
AUTOTRADER_DIR = HOME_DIR / '.autotrader'
CREDENTIALS_DIR = AUTOTRADER_DIR / 'credentials'
CONFIG_DIR = Path('configs')
DATA_DIR = Path('data')
LOGS_DIR = Path('logs')

# Docker image names
TELEGRAM_IMAGE = 'autotrader-telegram:latest'
TRADER_IMAGE = 'autotrader-trader:latest'

# ===============================
# Credential Constants
# ===============================

# Credential file formats
CREDENTIAL_JSON_EXTENSION = '.json'
CREDENTIAL_ENV_EXTENSION = '.env'
CREDENTIAL_DISPLAY_EXTENSION = '.display'

# Environment variable names
BYBIT_API_KEY_VAR = 'BYBIT_API_KEY'
BYBIT_API_SECRET_VAR = 'BYBIT_API_SECRET'
BYBIT_SECRET_KEY_VAR = 'BYBIT_SECRET_KEY'  # Alternative name

# Telegram environment variables
TELEGRAM_BOT_TOKEN_VAR = 'TELEGRAM_BOT_TOKEN'
TELEGRAM_CHAT_ID_VAR = 'TELEGRAM_CHAT_ID'

# ===============================
# File Permissions
# ===============================

# Secure file permissions for credentials
CREDENTIAL_FILE_PERMISSIONS = 0o600

# ===============================
# Default Values
# ===============================

# Default trading configuration
DEFAULT_TRADING_AMOUNT = 50.0
DEFAULT_SYMBOL_SUFFIX = '/USDT:USDT'
DEFAULT_DIRECTION = 'LONG'
DEFAULT_TEST_MODE = False

# Default credential profile
DEFAULT_CREDENTIAL_PROFILE = 'default'

# ===============================
# Utility Functions
# ===============================

def ensure_directories():
    """Ensure all required directories exist"""
    directories = [
        AUTOTRADER_DIR,
        CREDENTIALS_DIR,
        CONFIG_DIR,
        DATA_DIR,
        LOGS_DIR
    ]
    
    for directory in directories:
        directory.mkdir(parents=True, exist_ok=True)

def get_credential_file_path(profile: str, format_type: str = 'json') -> Path:
    """Get credential file path for a profile"""
    if format_type == 'json':
        return CREDENTIALS_DIR / f'{profile}{CREDENTIAL_JSON_EXTENSION}'
    elif format_type == 'env':
        return CREDENTIALS_DIR / f'{profile}{CREDENTIAL_ENV_EXTENSION}'
    elif format_type == 'display':
        return CREDENTIALS_DIR / f'{profile}{CREDENTIAL_DISPLAY_EXTENSION}'
    else:
        raise ValueError(f'Unknown credential format: {format_type}')

def get_environment_variable(var_name: str, default: str = '') -> str:
    """Get environment variable with default value"""
    return os.environ.get(var_name, default)

def normalize_symbol(symbol: str) -> str:
    """Normalize trading symbol to full format"""
    if '/' not in symbol:
        # Simple symbol like 'btc' -> 'BTC/USDT:USDT'
        return f'{symbol.upper()}{DEFAULT_SYMBOL_SUFFIX}'
    return symbol

def get_container_name(symbol: str) -> str:
    """Generate container name from symbol - consistent with shell_constants.sh"""
    # Extract base symbol (e.g., ETH/USDT:USDT -> eth, BTC/USDT:USDT -> btc)
    if "/" in symbol:
        # Full format like "ETH/USDT:USDT" -> "eth"
        base_symbol = symbol.split("/")[0]
    elif symbol.upper().endswith("USDT") and len(symbol) > 4:
        # Format like "ETHUSDT" -> "eth"
        base_symbol = symbol[:-4]
    else:
        # Simple format like "ETH" or "eth" -> "eth"
        base_symbol = symbol

    # Convert to lowercase
    base_symbol = base_symbol.lower()

    # Remove any existing 'usdt' suffix to avoid duplication
    if base_symbol.lower().endswith('usdt'):
        base_symbol = base_symbol[:-4]

    # Add 'usdt' suffix for clarity - consistent with shell_constants.sh
    return f"{base_symbol}usdt"

# ===============================
# Docker Constants
# ===============================

# Docker volume mounts (relative to project root)
DOCKER_VOLUME_MOUNTS = {
    'configs': f'{CONFIG_DIR}:/app/configs',
    'data': f'{DATA_DIR}:/app/data', 
    'logs': f'{LOGS_DIR}:/app/logs',
    'credentials': f'{CREDENTIALS_DIR}:/root/.autotrader/credentials',
    'docker_sock': '/var/run/docker.sock:/var/run/docker.sock'
}

# Docker environment variables
DOCKER_ENV_VARS = {
    'telegram': [
        TELEGRAM_BOT_TOKEN_VAR,
        TELEGRAM_CHAT_ID_VAR
    ],
    'trader': [
        BYBIT_API_KEY_VAR,
        BYBIT_API_SECRET_VAR
    ]
}
