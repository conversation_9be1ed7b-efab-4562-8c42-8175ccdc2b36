#!/usr/bin/env python3
"""
Direct test of cleanup logic to verify if Telegram bot can properly cleanup containers
"""

import os
import sys
import subprocess
import asyncio
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

def print_step(step, message):
    print(f"🔵 Step {step}: {message}")

def print_success(message):
    print(f"✅ {message}")

def print_error(message):
    print(f"❌ {message}")

def print_info(message):
    print(f"ℹ️ {message}")

def run_command(cmd, cwd=None):
    """Run shell command and return result"""
    try:
        result = subprocess.run(
            cmd, 
            shell=True, 
            capture_output=True, 
            text=True, 
            cwd=cwd or os.getcwd()
        )
        return result.returncode, result.stdout.strip(), result.stderr.strip()
    except Exception as e:
        return 1, "", str(e)

async def test_cleanup_logic():
    """Test the cleanup logic directly"""
    
    print("🧪 Testing Cleanup Logic Directly")
    print("=" * 40)
    
    # Step 1: Check current containers
    print_step(1, "Checking current containers")
    
    returncode, stdout, stderr = run_command("docker ps -a --format 'table {{.Names}}\\t{{.Status}}'")
    if returncode == 0:
        print("Current containers:")
        print(stdout)
    else:
        print_error(f"Failed to list containers: {stderr}")
        return False
    
    # Step 2: Test container naming logic
    print_step(2, "Testing container naming logic")
    
    # Test the shell function
    returncode, stdout, stderr = run_command("source src/core/shell_constants.sh && get_container_name 'eth'")
    if returncode == 0:
        shell_name = stdout.strip()
        print_success(f"Shell naming: 'eth' -> '{shell_name}'")
    else:
        print_error(f"Shell naming failed: {stderr}")
        return False
    
    # Step 3: Test cleanup commands directly
    print_step(3, "Testing cleanup commands directly")
    
    container_name = shell_name  # Use the name from shell function
    
    # Check if container exists
    returncode, stdout, stderr = run_command(f"docker ps -a --format '{{{{.Names}}}}' | grep '^{container_name}$'")
    
    if returncode == 0 and container_name in stdout:
        print_info(f"Container '{container_name}' exists - testing cleanup")
        
        # Test stop command
        print_info(f"Testing: docker stop {container_name}")
        returncode, stdout, stderr = run_command(f"docker stop {container_name}")
        if returncode == 0:
            print_success(f"Stop command succeeded: {stdout}")
        else:
            print_error(f"Stop command failed: {stderr}")
        
        # Test remove command
        print_info(f"Testing: docker rm {container_name}")
        returncode, stdout, stderr = run_command(f"docker rm {container_name}")
        if returncode == 0:
            print_success(f"Remove command succeeded: {stdout}")
        else:
            print_error(f"Remove command failed: {stderr}")
        
        # Verify cleanup
        returncode, stdout, stderr = run_command(f"docker ps -a --format '{{{{.Names}}}}' | grep '^{container_name}$'")
        if returncode != 0 or container_name not in stdout:
            print_success(f"Container '{container_name}' successfully cleaned up")
        else:
            print_error(f"Container '{container_name}' still exists after cleanup")
            
    else:
        print_info(f"Container '{container_name}' does not exist")
        
        # Create a test container for cleanup testing
        print_info("Creating test container for cleanup testing...")
        returncode, stdout, stderr = run_command(
            f"./bot.sh start eth --api-key 'test_key' --api-secret 'test_secret' --amount 50 --test-mode"
        )
        
        if returncode == 0:
            print_success("Test container created")
            
            # Wait a moment for container to start
            import time
            time.sleep(2)
            
            # Now test cleanup
            print_info("Testing cleanup on newly created container...")
            
            # Stop
            returncode, stdout, stderr = run_command(f"docker stop {container_name}")
            if returncode == 0:
                print_success("Stop succeeded")
            else:
                print_error(f"Stop failed: {stderr}")
            
            # Remove
            returncode, stdout, stderr = run_command(f"docker rm {container_name}")
            if returncode == 0:
                print_success("Remove succeeded")
            else:
                print_error(f"Remove failed: {stderr}")
                
        else:
            print_error(f"Failed to create test container: {stderr}")
    
    # Step 4: Test the exact commands that Telegram bot would use
    print_step(4, "Testing Telegram bot cleanup commands")
    
    # Simulate what the Telegram bot does
    cleanup_commands = [
        f"docker stop {container_name}",
        f"docker rm {container_name}"
    ]
    
    for cmd in cleanup_commands:
        print_info(f"Testing command: {cmd}")
        returncode, stdout, stderr = run_command(cmd)
        
        if returncode == 0:
            print_success(f"Command succeeded: {stdout}")
        else:
            # For cleanup commands, non-zero exit is often OK (container might not exist)
            print_info(f"Command returned {returncode}: {stderr}")
    
    print_success("Cleanup logic test completed!")
    return True

if __name__ == "__main__":
    try:
        result = asyncio.run(test_cleanup_logic())
        
        if result:
            print("\n🚀 Cleanup logic appears to work correctly!")
            print("\n📋 If /createbot still doesn't cleanup containers, the issue might be:")
            print("   1. Telegram bot not calling cleanup function")
            print("   2. Container naming mismatch")
            print("   3. Permission issues in Docker container")
            print("   4. Error handling preventing cleanup execution")
            
            print("\n🔍 Debug steps:")
            print("   1. Test /createbot and monitor: docker logs telegram-bot -f")
            print("   2. Check if cleanup function is called")
            print("   3. Verify container names match exactly")
        else:
            print("\n❌ Cleanup logic has issues - fix before testing /createbot")
            
    except Exception as e:
        print_error(f"Test failed with exception: {e}")
        sys.exit(1)
