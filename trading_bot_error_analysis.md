# Phân Tích Lỗi Trading Bot và Giải Pháp K<PERSON>ắc Phục

## 🚨 Tóm Tắt Lỗi

Bot trading gặp hai lỗi chính:
1. **Lỗi balance verification**: `float() argument must be a string or a real number, not 'NoneType'`
2. **Lỗi insufficient balance**: Bybit API error code 110007 "ab not enough for new order"

## 📊 Phân Tích Chi Tiết

### 1. Lỗi Balance Verification

**Vị trí**: `src/application/engine/position_handler.py:863`
**Nguyên nhân**: Method `fetch_balance()` trả về `None` hoặc cấu trúc dữ liệu không đúng

**Code hiện tại có vấn đề**:
```python
# Line 843-852 trong position_handler.py
balance = await self.position_manager.exchange.fetch_balance()
available_balance = 0.0

# Parse USDT balance safely
if isinstance(balance, dict) and 'free' in balance and isinstance(balance['free'], dict):
    available_balance = float(balance['free'].get('USDT', 0))  # ❌ Có thể lỗi nếu get('USDT') trả về None
elif isinstance(balance, dict) and 'USDT' in balance and isinstance(balance['USDT'], dict):
    available_balance = float(balance['USDT'].get('free', 0))   # ❌ Tương tự
```

### 2. Lỗi Insufficient Balance

**Vị trí**: Bybit API response
**Nguyên nhân**: 
- Account không đủ balance để tạo order với position size 10.0 @ $158.97
- Required margin ≈ $1589.7 (nếu leverage 1x) hoặc ít hơn nếu có leverage cao hơn

## 🔧 Giải Pháp Khắc Phục

### 1. Cải Thiện Error Handling cho Balance

**File**: `src/application/engine/position_handler.py`

```python
# Thay thế đoạn code từ line 843-863
try:
    balance = await self.position_manager.exchange.fetch_balance()
    available_balance = 0.0
    
    # Improved balance parsing with better error handling
    if balance is None:
        self.logger.warning(f"⚠️ Balance fetch returned None for DCA {dca_type}")
        return
        
    if isinstance(balance, dict):
        # Try different balance structures
        usdt_balance = None
        
        # Structure 1: {'free': {'USDT': '100.0'}}
        if 'free' in balance and isinstance(balance['free'], dict):
            usdt_balance = balance['free'].get('USDT')
            
        # Structure 2: {'USDT': {'free': '100.0'}}
        elif 'USDT' in balance and isinstance(balance['USDT'], dict):
            usdt_balance = balance['USDT'].get('free')
            
        # Structure 3: Direct USDT value
        elif 'USDT' in balance:
            usdt_balance = balance['USDT']
            
        # Safe float conversion
        if usdt_balance is not None:
            try:
                available_balance = float(usdt_balance) if usdt_balance != '' else 0.0
            except (ValueError, TypeError) as e:
                self.logger.warning(f"⚠️ Could not parse USDT balance '{usdt_balance}': {e}")
                available_balance = 0.0
        else:
            self.logger.warning(f"⚠️ USDT balance not found in balance response: {balance}")
            
    else:
        self.logger.warning(f"⚠️ Invalid balance format: {type(balance)} - {balance}")
        return
        
    self.logger.debug(f"💰 Balance check: Available=${available_balance:.2f}, Required=${required_margin:.2f}")
    
    if available_balance < required_margin:
        self.logger.warning(
            f"⚠️ Insufficient balance for DCA {dca_type}: "
            f"Available=${available_balance:.2f}, Required=${required_margin:.2f} "
            f"(${usd_amount:.2f} @ {self.config.risk.leverage}x leverage)"
        )
        return
        
except Exception as balance_error:
    self.logger.warning(f"⚠️ Could not verify balance for DCA {dca_type}: {balance_error}")
    # Log more details for debugging
    self.logger.debug(f"Balance error details: {type(balance_error).__name__}: {balance_error}")
    return  # ✅ Return instead of continuing - don't create order if balance check fails
```

### 2. Cải Thiện Position Size Calculation

**File**: `src/application/engine/position_handler.py`

Thêm logic để điều chỉnh position size dựa trên available balance:

```python
# Thêm sau balance check, trước khi tạo order
def _adjust_position_size_for_balance(self, target_usd_amount: float, 
                                    available_balance: float, 
                                    leverage: float) -> float:
    """Adjust position size based on available balance"""
    max_affordable_usd = available_balance * leverage * 0.95  # 95% of available to leave buffer
    
    if target_usd_amount > max_affordable_usd:
        self.logger.warning(
            f"⚠️ Adjusting position size: Target=${target_usd_amount:.2f} > "
            f"Affordable=${max_affordable_usd:.2f} (Balance=${available_balance:.2f} @ {leverage}x)"
        )
        return max_affordable_usd
    
    return target_usd_amount
```

### 3. Cải Thiện Exchange Connector Error Handling

**File**: `src/infrastructure/exchange/exchange_connector.py`

```python
# Thêm vào method _retry_request (line 224-233)
async def _retry_request(self, func, *args, **kwargs) -> Any:
    """Retry failed requests with exponential backoff"""
    for attempt in range(self.max_retries):
        try:
            result = await func(*args, **kwargs)
            return result
        except Exception as e:
            error_str = str(e)
            
            # Check for specific Bybit errors that shouldn't be retried
            if "110007" in error_str or "ab not enough" in error_str.lower():
                self.logger.error(f"Insufficient balance error (no retry): {error_str}")
                raise e
                
            if attempt == self.max_retries - 1:
                self.logger.error(f"Final retry attempt failed: {error_str}")
                raise e
            
            wait_time = 2 ** attempt
            self.logger.warning(f"Request failed (attempt {attempt + 1}), retrying in {wait_time}s: {error_str}")
            await asyncio.sleep(wait_time)
```

### 4. Thêm Balance Pre-Check vào Order Manager

**File**: `src/application/managers/order_manager.py`

```python
# Thêm method mới để check balance trước khi tạo order
async def _check_sufficient_balance(self, symbol: str, usd_amount: float, leverage: float = 1.0) -> tuple[bool, float]:
    """Check if account has sufficient balance for order"""
    try:
        balance = await self.exchange.fetch_balance()
        
        if balance is None:
            return False, 0.0
            
        available_balance = 0.0
        
        if isinstance(balance, dict):
            if 'free' in balance and isinstance(balance['free'], dict):
                usdt_balance = balance['free'].get('USDT')
            elif 'USDT' in balance and isinstance(balance['USDT'], dict):
                usdt_balance = balance['USDT'].get('free')
            else:
                usdt_balance = balance.get('USDT')
                
            if usdt_balance is not None:
                try:
                    available_balance = float(usdt_balance) if usdt_balance != '' else 0.0
                except (ValueError, TypeError):
                    return False, 0.0
                    
        required_margin = usd_amount / leverage
        return available_balance >= required_margin, available_balance
        
    except Exception as e:
        self.logger.error(f"❌ Balance check error: {e}")
        return False, 0.0

# Modify create_dca_order method (line 360-400)
async def create_dca_order(self, symbol: str, side: str, entry_price: float, 
                         position_size: float, dca_type: str) -> Optional[str]:
    """Create DCA order with improved balance checking"""
    try:
        # Calculate USD amount
        usd_amount = position_size * entry_price
        leverage = getattr(self.config.risk, 'leverage', 1.0)
        
        # Check balance before creating order
        has_balance, available_balance = await self._check_sufficient_balance(symbol, usd_amount, leverage)
        
        if not has_balance:
            self.logger.warning(
                f"⚠️ Insufficient balance for DCA {dca_type}: "
                f"Available=${available_balance:.2f}, Required=${usd_amount/leverage:.2f} "
                f"(${usd_amount:.2f} @ {leverage}x leverage)"
            )
            return None
            
        # Proceed with original order creation logic...
        # ... rest of existing code
```

## ⚙️ Immediate Actions Required

1. **Apply the balance parsing fix** to prevent NoneType errors
2. **Add balance pre-checks** before creating orders  
3. **Adjust position sizing** based on available balance
4. **Improve error handling** for Bybit-specific errors

## 🔍 Debug Steps

1. **Check actual account balance** on Bybit
2. **Verify API credentials** have sufficient permissions
3. **Review leverage settings** in configuration
4. **Monitor balance format** returned by Bybit API

## ✅ Testing Recommendations

1. Test with small position sizes first
2. Add more detailed logging for balance responses
3. Implement position size auto-adjustment
4. Add balance alerts/notifications

## 🚀 Long-term Improvements

1. **Real-time balance monitoring**
2. **Dynamic position sizing** based on available capital
3. **Risk management improvements**
4. **Better error recovery mechanisms**