# Container Naming Consistency Fix

## Vấn đề đã được gi<PERSON>i quyết

**Bug:** Container Naming Mismatch Causes Telegram Bot Issues

Trước đây có sự không nhất quán trong việc đặt tên Docker container giữa các thành phần:

- **BotCreator CLI** (`src/cli/bot_creator.py`): Chỉ sử dụng base symbol viết thường (ví dụ: "eth")
- **Telegram Bot Handlers** (`src/infrastructure/telegram/handlers/base_handler.py`): Mong đợi container có suffix "usdt" (ví dụ: "ethusdt")
- **Shell Scripts** (`src/core/shell_constants.sh`): Cũng sử dụng suffix "usdt"

Điều này khiến các lệnh quản lý Telegram (status, stop, logs) không thể tìm thấy và điều khiển container được tạo bởi CLI.

## Giải pháp đã triển khai

### 1. Thống nhất naming convention

Tất cả các thành phần hiện tại đều sử dụng cùng một logic đặt tên container:
- **Format:** `{base_symbol}usdt`
- **Ví dụ:** 
  - `ETH` → `ethusdt`
  - `BTC/USDT:USDT` → `btcusdt`
  - `ETHUSDT` → `ethusdt`

### 2. Các file đã được cập nhật

#### `src/cli/bot_creator.py`
- Cập nhật method `normalize_symbol()` để thêm suffix "usdt"
- Xử lý các format symbol khác nhau
- Tránh trùng lặp suffix "usdt"

#### `src/infrastructure/telegram/handlers/base_handler.py`
- Cải thiện method `_get_container_name()` để xử lý tất cả format symbol
- Đảm bảo nhất quán với shell_constants.sh

#### `src/utils/container_helper.py`
- Cập nhật method `normalize_symbol()` để sử dụng logic mới
- Đảm bảo CLI commands có thể tìm thấy container đúng cách

#### `src/core/constants.py`
- Cập nhật function `get_container_name()` để nhất quán
- Sử dụng cùng logic với các thành phần khác

#### `src/infrastructure/telegram/handlers/bot_management_handler.py`
- Cập nhật `symbol_map` để hỗ trợ naming convention mới
- Giữ legacy support cho compatibility

### 3. Logic xử lý symbol

```python
def get_container_name(symbol: str) -> str:
    # Extract base symbol
    if "/" in symbol:
        # Full format like "ETH/USDT:USDT" -> "eth"
        base_symbol = symbol.split("/")[0]
    elif symbol.upper().endswith("USDT") and len(symbol) > 4:
        # Format like "ETHUSDT" -> "eth"
        base_symbol = symbol[:-4]
    else:
        # Simple format like "ETH" or "eth" -> "eth"
        base_symbol = symbol
    
    # Convert to lowercase
    base_symbol = base_symbol.lower()
    
    # Remove any existing 'usdt' suffix to avoid duplication
    base_symbol = base_symbol.rstrip('usdt')
    
    # Add 'usdt' suffix for clarity
    return f"{base_symbol}usdt"
```

## Kết quả

### ✅ Trước khi sửa
- CLI tạo container: `eth`
- Telegram bot tìm: `ethusdt`
- **Kết quả:** Không tìm thấy container ❌

### ✅ Sau khi sửa
- CLI tạo container: `ethusdt`
- Telegram bot tìm: `ethusdt`
- **Kết quả:** Tìm thấy và quản lý được container ✅

## Test Coverage

Đã test với các format symbol sau:
- Simple: `eth`, `ETH`, `btc`, `BTC`
- Full format: `ETH/USDT:USDT`, `BTC/USDT:USDT`
- USDT format: `ETHUSDT`, `BTCUSDT`
- Duplication handling: `ethusdt`, `btcusdt`

Tất cả đều trả về kết quả nhất quán: `{base}usdt`

## Backward Compatibility

- Giữ legacy support trong `bot_management_handler.py`
- Container cũ vẫn có thể được quản lý
- Không breaking changes cho existing containers

## Lợi ích

1. **Telegram bot commands hoạt động đúng:** Status, stop, logs, restart
2. **CLI và Telegram bot đồng bộ:** Cùng tìm thấy container
3. **Consistency:** Tất cả thành phần sử dụng cùng naming convention
4. **Maintainability:** Dễ dàng maintain và debug
5. **User experience:** Không còn confusion về container names
