#!/usr/bin/env python3
"""Test return code fix in telegram handlers"""

import sys
import os
import asyncio

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.infrastructure.telegram.telegram_base import TelegramBase

async def test_return_code_fix():
    """Test that return codes are handled correctly"""
    print("🧪 Testing Return Code Fix")
    print("=" * 40)
    
    # Create TelegramBase instance
    telegram_base = TelegramBase()
    
    # Test successful command (should return 0)
    print("\n📋 Testing successful command:")
    print("Command: echo 'test'")
    
    try:
        returncode, stdout, stderr = await telegram_base.execute_botsh_command(['echo', 'test'])
        
        print(f"Return code: {returncode}")
        print(f"Stdout: '{stdout}'")
        print(f"Stderr: '{stderr}'")
        
        if returncode == 0:
            print("✅ Success command correctly returns 0")
        else:
            print(f"❌ Success command incorrectly returns {returncode}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing successful command: {e}")
        return False
    
    # Test failing command (should return non-zero)
    print("\n📋 Testing failing command:")
    print("Command: false")
    
    try:
        returncode, stdout, stderr = await telegram_base.execute_botsh_command(['false'])
        
        print(f"Return code: {returncode}")
        print(f"Stdout: '{stdout}'")
        print(f"Stderr: '{stderr}'")
        
        if returncode != 0:
            print("✅ Failing command correctly returns non-zero")
        else:
            print(f"❌ Failing command incorrectly returns {returncode}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing failing command: {e}")
        return False
    
    # Test bot.sh help command (should return 0)
    print("\n📋 Testing bot.sh help command:")
    print("Command: ./bot.sh help")
    
    try:
        returncode, stdout, stderr = await telegram_base.execute_botsh_command(['./bot.sh', 'help'])
        
        print(f"Return code: {returncode}")
        print(f"Stdout length: {len(stdout)} chars")
        print(f"Stderr: '{stderr}'")
        
        if returncode == 0:
            print("✅ bot.sh help correctly returns 0")
        else:
            print(f"❌ bot.sh help incorrectly returns {returncode}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing bot.sh help: {e}")
        return False
    
    print("\n" + "=" * 40)
    print("🎉 All return code tests passed!")
    return True

if __name__ == "__main__":
    result = asyncio.run(test_return_code_fix())
    sys.exit(0 if result else 1)
