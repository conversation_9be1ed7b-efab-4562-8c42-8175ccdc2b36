# Tests

This directory contains all test files and testing utilities for the project.

## Test Files

### Python Test Files
- `auto_test_createbot.py` - Automated bot creation tests
- `demo_integration.py` - Integration demo tests
- `fix_wizard_test.py` - Fix wizard testing
- `simple_telegram_test.py` - Simple Telegram bot tests
- `test_*.py` - Various unit and integration tests
- `verify_*.py` - Verification scripts

### Shell Test Scripts
- `test_*.sh` - Shell script tests
- `verify_*.sh` - Verification shell scripts

## Running Tests

To run the tests, use the appropriate test runner for each file type:

```bash
# Python tests
python tests/test_filename.py

# Shell script tests
bash tests/test_script.sh
```

## Test Categories

- **Unit Tests**: Individual component testing
- **Integration Tests**: Component interaction testing
- **Bot Tests**: Telegram bot functionality testing
- **Docker Tests**: Container and deployment testing
- **Verification Tests**: System verification and validation
