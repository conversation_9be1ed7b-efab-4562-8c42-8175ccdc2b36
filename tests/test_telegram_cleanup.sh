#!/bin/bash
# Test script for Telegram /createbot cleanup logic
# Tests if Telegram bot correctly cleans up existing containers

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

echo "🧪 Testing Telegram /createbot Cleanup Logic"
echo "============================================="

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_step() {
    echo -e "${BLUE}📋 Step $1: $2${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${YELLOW}ℹ️ $1${NC}"
}

# Step 1: Check current containers
print_step "1" "Checking current containers"
echo "Current containers:"
docker ps --format "table {{.Names}}\t{{.Status}}" | grep -E "(telegram-bot|ethusdt|btcusdt)" || echo "No relevant containers found"

# Step 2: Verify container naming
print_step "2" "Verifying container naming consistency"

# Test shell naming
source src/core/shell_constants.sh
test_symbols=("eth" "ETH" "ETH/USDT:USDT" "btc" "BTC")

echo "Shell container naming:"
for symbol in "${test_symbols[@]}"; do
    container_name=$(get_container_name "$symbol")
    echo "   $symbol -> $container_name"
done

# Step 3: Test cleanup scenario
print_step "3" "Testing cleanup scenario"

# Check if ethusdt container exists
if docker ps -a --format "{{.Names}}" | grep -q "^ethusdt$"; then
    print_info "Container 'ethusdt' exists - perfect for testing cleanup"
    
    # Show container details
    echo "Container details:"
    docker ps -a --format "table {{.Names}}\t{{.Status}}\t{{.CreatedAt}}" | grep ethusdt
    
    print_info "This container should be cleaned up when /createbot creates a new 'eth' bot"
else
    print_error "Container 'ethusdt' does not exist"
    print_info "Creating test container..."
    
    # Create test container
    ./bot.sh start eth --api-key "test_key" --api-secret "test_secret" --amount 50 --test-mode
    
    if docker ps -a --format "{{.Names}}" | grep -q "^ethusdt$"; then
        print_success "Test container 'ethusdt' created"
    else
        print_error "Failed to create test container"
        exit 1
    fi
fi

# Step 4: Instructions for manual testing
print_step "4" "Manual testing instructions"

echo ""
echo "🚀 Ready to test /createbot cleanup logic:"
echo ""
echo "1. Open Telegram and find your bot"
echo "2. Send: /createbot"
echo "3. When asked for symbol, enter: eth"
echo "4. Continue with the wizard (amount: 100, direction: LONG)"
echo "5. Watch the logs to see if cleanup happens:"
echo ""
echo "   Monitor command: docker logs telegram-bot -f"
echo ""
echo "Expected behavior:"
echo "   ✅ Bot should detect existing 'ethusdt' container"
echo "   ✅ Bot should stop and remove the existing container"
echo "   ✅ Bot should create a new 'ethusdt' container"
echo "   ✅ Bot should report success"
echo ""

# Step 5: Monitoring commands
print_step "5" "Monitoring commands"

echo "Use these commands to monitor the process:"
echo ""
echo "# Watch containers in real-time"
echo "watch 'docker ps --format \"table {{.Names}}\\t{{.Status}}\"'"
echo ""
echo "# Monitor Telegram bot logs"
echo "docker logs telegram-bot -f"
echo ""
echo "# Check specific container logs"
echo "docker logs ethusdt -f"
echo ""

# Step 6: Verification commands
print_step "6" "Verification commands after testing"

echo "After running /createbot, verify with these commands:"
echo ""
echo "# Check if old container was replaced"
echo "docker ps -a --format 'table {{.Names}}\\t{{.Status}}\\t{{.CreatedAt}}' | grep ethusdt"
echo ""
echo "# Check container logs for startup"
echo "docker logs ethusdt --tail 20"
echo ""
echo "# Verify container is running correctly"
echo "docker exec ethusdt ps aux | grep python"
echo ""

print_success "Test setup complete!"
echo ""
echo "🎯 Key things to verify:"
echo "   1. Container naming: 'eth' symbol -> 'ethusdt' container"
echo "   2. Cleanup logic: existing container is stopped and removed"
echo "   3. Recreation: new container is created with same name"
echo "   4. No errors: process completes successfully"
echo ""
echo "📱 Now test /createbot in Telegram!"
