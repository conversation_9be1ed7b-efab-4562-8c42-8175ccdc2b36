#!/usr/bin/env python3
"""
Test script to verify container naming consistency between bot.sh and Telegram handlers
"""

import os
import sys
import subprocess
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

def print_step(step, message):
    print(f"🔵 Step {step}: {message}")

def print_success(message):
    print(f"✅ {message}")

def print_error(message):
    print(f"❌ {message}")

def print_info(message):
    print(f"ℹ️ {message}")

def run_shell_command(cmd):
    """Run shell command and return result"""
    try:
        result = subprocess.run(
            cmd, 
            shell=True, 
            capture_output=True, 
            text=True, 
            cwd=os.getcwd()
        )
        return result.returncode == 0, result.stdout.strip(), result.stderr.strip()
    except Exception as e:
        return False, "", str(e)

def get_shell_container_name(symbol):
    """Get container name using shell function"""
    cmd = f"source src/core/shell_constants.sh && get_container_name '{symbol}'"
    success, stdout, stderr = run_shell_command(cmd)
    if success:
        return stdout.strip()
    else:
        print_error(f"Shell command failed: {stderr}")
        return None

def get_python_container_name(symbol):
    """Get container name using Python logic"""
    try:
        # Import the base handler to test the naming logic
        from src.infrastructure.telegram.handlers.base_handler import BaseHandler

        # Create a mock handler instance
        handler = BaseHandler()
        return handler._get_container_name(symbol)
    except Exception as e:
        print_error(f"Python import failed: {e}")
        return None

def get_botcreator_container_name(symbol):
    """Get container name using BotCreator logic"""
    try:
        # Import the bot creator to test the naming logic
        from src.cli.bot_creator import BotCreator

        # Create a bot creator instance
        creator = BotCreator()
        return creator.normalize_symbol(symbol)
    except Exception as e:
        print_error(f"BotCreator import failed: {e}")
        return None

def test_container_naming():
    """Test container naming consistency"""
    
    print("🧪 Testing Container Naming Consistency")
    print("=" * 50)
    
    # Test cases
    test_symbols = [
        "eth",
        "ETH", 
        "btc",
        "BTC",
        "sol",
        "SOL",
        "ETH/USDT:USDT",
        "BTC/USDT:USDT",
        "SOL/USDT:USDT"
    ]
    
    print_step(1, "Testing shell vs Python vs BotCreator naming consistency")

    all_consistent = True

    for symbol in test_symbols:
        print(f"\n📋 Testing symbol: '{symbol}'")

        # Get names from all sources
        shell_name = get_shell_container_name(symbol)
        python_name = get_python_container_name(symbol)
        botcreator_name = get_botcreator_container_name(symbol)

        if shell_name and python_name and botcreator_name:
            if shell_name == python_name == botcreator_name:
                print_success(f"Consistent: '{symbol}' -> '{shell_name}'")
            else:
                print_error(f"Inconsistent: '{symbol}' -> Shell: '{shell_name}', Python: '{python_name}', BotCreator: '{botcreator_name}'")
                all_consistent = False
        else:
            print_error(f"Failed to get names for '{symbol}'")
            all_consistent = False
    
    print("\n" + "=" * 50)
    if all_consistent:
        print_success("All container names are consistent!")
    else:
        print_error("Container naming is inconsistent!")
    
    # Step 2: Test expected naming pattern
    print_step(2, "Testing expected naming pattern")
    
    expected_mappings = {
        "eth": "ethusdt",
        "ETH": "ethusdt",
        "btc": "btcusdt",
        "BTC": "btcusdt",
        "sol": "solusdt",
        "SOL": "solusdt",
        "ETH/USDT:USDT": "ethusdt",
        "BTC/USDT:USDT": "btcusdt",
        "SOL/USDT:USDT": "solusdt",
        "ETHUSDT": "ethusdt",
        "BTCUSDT": "btcusdt",
        "SOLUSDT": "solusdt",
        "ethusdt": "ethusdt",  # Test duplication handling
        "btcusdt": "btcusdt"   # Test duplication handling
    }
    
    pattern_correct = True
    
    for symbol, expected in expected_mappings.items():
        shell_name = get_shell_container_name(symbol)
        if shell_name == expected:
            print_success(f"Pattern correct: '{symbol}' -> '{shell_name}'")
        else:
            print_error(f"Pattern wrong: '{symbol}' -> Expected: '{expected}', Got: '{shell_name}'")
            pattern_correct = False
    
    print("\n" + "=" * 50)
    if pattern_correct:
        print_success("All naming patterns are correct!")
    else:
        print_error("Some naming patterns are wrong!")
    
    # Step 3: Test cleanup scenarios
    print_step(3, "Testing cleanup scenarios")
    
    print_info("Testing container cleanup with correct names...")
    
    # Check if any test containers exist
    test_containers = ["ethusdt", "btcusdt", "solusdt"]
    
    for container in test_containers:
        success, stdout, stderr = run_shell_command(f"docker ps -a --format '{{{{.Names}}}}' | grep '^{container}$'")
        if success and container in stdout:
            print_info(f"Container '{container}' exists - would be cleaned up")
        else:
            print_info(f"Container '{container}' does not exist")
    
    return all_consistent and pattern_correct

if __name__ == "__main__":
    success = test_container_naming()
    
    if success:
        print("\n🚀 Container naming is consistent and ready for /createbot testing!")
        print("\n📋 Next steps:")
        print("   1. Test /createbot in Telegram")
        print("   2. Verify container names match expected pattern")
        print("   3. Test cleanup when container already exists")
    else:
        print("\n❌ Fix container naming issues before testing /createbot")
    
    sys.exit(0 if success else 1)
