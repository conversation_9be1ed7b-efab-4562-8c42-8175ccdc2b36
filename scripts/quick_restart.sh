#!/bin/bash
set -e

echo "🔄 Quick restart..."

# Stop and remove
docker stop autotrader-telegram 2>/dev/null || true
docker rm autotrader-telegram 2>/dev/null || true

# Build
docker build -t autotrader-telegram -f Dockerfile.telegram .

# Get Docker group ID from host
DOCKER_GID=$(stat -c '%g' /var/run/docker.sock)

# Run with Docker socket and correct group ID
docker run -d \
    --name autotrader-telegram \
    --env-file .env \
    -v $(pwd):/app \
    -v /var/run/docker.sock:/var/run/docker.sock \
    --group-add $DOCKER_GID \
    autotrader-telegram

echo "✅ Done! Check with: docker logs autotrader-telegram"
