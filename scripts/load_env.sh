#!/bin/bash
# Load environment variables from .env file

# Check if .env file exists
if [[ -f ".env" ]]; then
    echo "📁 Loading environment variables from .env file..."
    
    # Export variables from .env file
    set -a  # automatically export all variables
    source .env
    set +a  # stop automatically exporting
    
    echo "✅ Environment variables loaded successfully"
    
    # Verify required variables are set
    if [[ -n "$TELEGRAM_BOT_TOKEN" ]]; then
        echo "   ✅ TELEGRAM_BOT_TOKEN: ${TELEGRAM_BOT_TOKEN:0:10}..."
    else
        echo "   ❌ TELEGRAM_BOT_TOKEN not found in .env"
    fi
    
    if [[ -n "$TELEGRAM_CHAT_ID" ]]; then
        echo "   ✅ TELEGRAM_CHAT_ID: $TELEGRAM_CHAT_ID"
    else
        echo "   ❌ TELEGRAM_CHAT_ID not found in .env"
    fi
    
    if [[ -n "$BYBIT_API_KEY" ]]; then
        echo "   ✅ BYBIT_API_KEY: ${BYBIT_API_KEY:0:10}..."
    else
        echo "   ⚠️ BYBIT_API_KEY not found in .env (optional)"
    fi
    
else
    echo "⚠️ .env file not found"
    echo "💡 Create one from template: cp .env.example .env"
    echo "💡 Then edit .env with your actual credentials"
fi
