# Scripts

This directory contains utility scripts for project management and operations.

## Available Scripts

### Bot Management
- `quick_restart.sh` - Quick bot restart
- `restart_bot.sh` - Full bot restart
- `simple_restart.sh` - Simple restart procedure

### Docker & Container Management
- `check_docker.sh` - Docker status check
- `setup-container.sh` - Container setup

### Telegram Operations
- `get_chat_id.sh` - Get Telegram chat ID
- `telegram_quick_start.sh` - Quick Telegram setup

### Environment & Configuration
- `load_env.sh` - Load environment variables

## Usage

Make scripts executable before running:

```bash
chmod +x scripts/script_name.sh
./scripts/script_name.sh
```

## Main Scripts

The main execution scripts remain in the root directory:
- `bot.sh` - Main bot execution script
- `run.sh` - Main application runner
