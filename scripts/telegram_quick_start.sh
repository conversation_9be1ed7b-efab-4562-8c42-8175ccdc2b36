#!/bin/bash
# Telegram Bot Quick Start Script

echo "🤖 Telegram Bot Quick Start"
echo "=========================="
echo ""

# Check if telegram credentials are set
if [ -z "$TELEGRAM_BOT_TOKEN" ] || [ -z "$TELEGRAM_CHAT_ID" ]; then
    echo "❌ Telegram credentials not found!"
    echo ""
    echo "Please set environment variables:"
    echo "export TELEGRAM_BOT_TOKEN='your_bot_token'"
    echo "export TELEGRAM_CHAT_ID='your_chat_id'"
    echo ""
    echo "📖 See TELEGRAM_SETUP.md for instructions"
    exit 1
fi

echo "✅ Telegram credentials found"
echo "   Bot Token: ${TELEGRAM_BOT_TOKEN:0:10}..."
echo "   Chat ID: $TELEGRAM_CHAT_ID"
echo ""

# Test telegram connection
echo "🔍 Testing Telegram connection..."
RESPONSE=$(curl -s -X POST "https://api.telegram.org/bot${TELEGRAM_BOT_TOKEN}/sendMessage" \
    -d "chat_id=${TELEGRAM_CHAT_ID}" \
    -d "text=🚀 Trading Bot Telegram Test Message - $(date)")

if echo "$RESPONSE" | grep -q '"ok":true'; then
    echo "✅ Test message sent successfully!"
    echo ""
    echo "Check your Telegram for the test message."
else
    echo "❌ Failed to send test message"
    echo "Response: $RESPONSE"
    exit 1
fi

echo ""
echo "🚀 Starting Trading Bot with Telegram..."
echo "========================================"
echo ""

# Run bot with test mode and telegram
./bot.sh start hyper --test-mode --debug 