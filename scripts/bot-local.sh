#!/bin/bash
# AutoTrader Local Development & Testing Script
# Build and test Docker images locally without registry dependencies

set -e

# Script Version
SCRIPT_VERSION="3.0.0-local"
BUILD_MODE="local"

# Local Configuration
LOCAL_TAG_PREFIX="autotrader-local"
TELEGRAM_IMAGE="$LOCAL_TAG_PREFIX-telegram:latest"
TRADER_IMAGE="$LOCAL_TAG_PREFIX-trader:latest"

# Development Configuration
DEV_MODE=${DEV_MODE:-true}
DEBUG_MODE=${DEBUG_MODE:-false}
REBUILD_ON_START=${REBUILD_ON_START:-false}

# Telegram Configuration (same as production)
TELEGRAM_BOT_TOKEN=${TELEGRAM_BOT_TOKEN:-""}
TELEGRAM_CHAT_ID=${TELEGRAM_CHAT_ID:-""}
TELEGRAM_ENABLED=${TELEGRAM_ENABLED:-true}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

log_debug() {
    if [[ "$DEBUG_MODE" == "true" ]]; then
        echo -e "${PURPLE}🐛 DEBUG: $1${NC}"
    fi
}

# Check if Telegram is available
check_telegram_available() {
    if [[ "$TELEGRAM_ENABLED" != "true" ]]; then
        return 1
    fi
    
    if [[ -z "$TELEGRAM_BOT_TOKEN" ]] || [[ -z "$TELEGRAM_CHAT_ID" ]]; then
        return 1
    fi
    
    if ! command -v python3 &> /dev/null; then
        return 1
    fi
    
    return 0
}

# Send Telegram notification (simplified for local)
send_telegram_notification() {
    local message="$1"
    local command="${2:-""}"
    
    if ! check_telegram_available; then
        log_debug "Telegram not available, skipping notification"
        return 0
    fi
    
    log_debug "Sending Telegram notification: $message"
    
    # Activate virtual environment if available
    if [[ -f ".venv/bin/activate" ]]; then
        source .venv/bin/activate
    fi
    
    if [[ -n "$command" ]]; then
        python run_telegram_app.py send \
            --token "$TELEGRAM_BOT_TOKEN" \
            --chat-id "$TELEGRAM_CHAT_ID" \
            --message "🏠 LOCAL: $message" &
    else
        python run_telegram_app.py send \
            --token "$TELEGRAM_BOT_TOKEN" \
            --chat-id "$TELEGRAM_CHAT_ID" \
            --message "🏠 LOCAL: $message" &
    fi
}

# Check Docker
check_docker() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker not found. Please install Docker."
        return 1
    fi
    
    if ! docker version &>/dev/null; then
        log_error "Cannot connect to Docker daemon."
        return 1
    fi
    
    return 0
}

# Build Docker images locally
build_images() {
    local mode="${1:-both}"  # telegram, trader, or both
    local force_rebuild="${2:-false}"
    
    log_info "Building Docker images locally (mode: $mode)"
    
    if ! check_docker; then
        return 1
    fi
    
    case "$mode" in
        telegram)
            build_telegram_image "$force_rebuild"
            ;;
        trader)
            build_trader_image "$force_rebuild"
            ;;
        both|*)
            build_telegram_image "$force_rebuild"
            build_trader_image "$force_rebuild"
            ;;
    esac
}

# Build Telegram image
build_telegram_image() {
    local force_rebuild="${1:-false}"
    
    log_info "Building Telegram bot image: $TELEGRAM_IMAGE"
    
    # Check if image exists and force_rebuild is false
    if [[ "$force_rebuild" == "false" ]] && docker images -q "$TELEGRAM_IMAGE" &>/dev/null; then
        log_warning "Telegram image already exists. Use --rebuild to force rebuild."
        return 0
    fi
    
    if [[ ! -f "Dockerfile.telegram" ]]; then
        log_error "Dockerfile.telegram not found!"
        return 1
    fi
    
    log_info "Building from Dockerfile.telegram..."
    if docker build -f Dockerfile.telegram -t "$TELEGRAM_IMAGE" .; then
        log_success "Telegram image built successfully: $TELEGRAM_IMAGE"
        send_telegram_notification "Telegram image built locally: $TELEGRAM_IMAGE"
    else
        log_error "Failed to build Telegram image"
        return 1
    fi
}

# Build Trader image
build_trader_image() {
    local force_rebuild="${1:-false}"
    
    log_info "Building Trader bot image: $TRADER_IMAGE"
    
    # Check if image exists and force_rebuild is false
    if [[ "$force_rebuild" == "false" ]] && docker images -q "$TRADER_IMAGE" &>/dev/null; then
        log_warning "Trader image already exists. Use --rebuild to force rebuild."
        return 0
    fi
    
    if [[ ! -f "Dockerfile.trader" ]]; then
        log_error "Dockerfile.trader not found!"
        return 1
    fi
    
    log_info "Building from Dockerfile.trader..."
    if docker build -f Dockerfile.trader -t "$TRADER_IMAGE" .; then
        log_success "Trader image built successfully: $TRADER_IMAGE"
        send_telegram_notification "Trader image built locally: $TRADER_IMAGE"
    else
        log_error "Failed to build Trader image"
        return 1
    fi
}

# Generate container name from symbol
generate_container_name() {
    local symbol="$1"
    if [[ -z "$symbol" ]]; then
        echo "local-crypto-trading-bot"
    else
        local base_symbol=$(echo "$symbol" | cut -d'/' -f1 | cut -d':' -f1)
        local normalized=$(echo "$base_symbol" | tr '[:upper:]' '[:lower:]')
        echo "local-$normalized"
    fi
}

# Parse trading arguments (same as production)
parse_trading_arguments() {
    TRADE_SYMBOL=""
    TRADE_AMOUNT=""
    TRADE_EXCHANGE=""
    TRADE_DIRECTION=""
    TRADE_TEST_MODE=""
    TRADE_STOP_LOSS=""
    TRADE_TAKE_PROFIT=""
    TRADE_API_KEY=""
    TRADE_API_SECRET=""
    
    # Check if first argument is a symbol
    if [[ $# -gt 0 ]] && [[ ! "$1" =~ ^-- ]]; then
        TRADE_SYMBOL="$1"
        shift
    fi
    
    # Parse arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            --symbol)
                TRADE_SYMBOL="$2"
                shift 2
                ;;
            --amount)
                TRADE_AMOUNT="$2"
                shift 2
                ;;
            --exchange)
                TRADE_EXCHANGE="$2"
                shift 2
                ;;
            --direction)
                TRADE_DIRECTION="$2"
                shift 2
                ;;
            --test-mode)
                TRADE_TEST_MODE="true"
                shift
                ;;
            --stop-loss)
                TRADE_STOP_LOSS="$2"
                shift 2
                ;;
            --take-profit)
                TRADE_TAKE_PROFIT="$2"
                shift 2
                ;;
            --key)
                TRADE_API_KEY="$2"
                export BYBIT_API_KEY="$2"
                shift 2
                ;;
            --secret)
                TRADE_API_SECRET="$2"
                export BYBIT_API_SECRET="$2"
                shift 2
                ;;
            --debug)
                DEBUG_MODE="true"
                shift
                ;;
            --rebuild)
                REBUILD_ON_START="true"
                shift
                ;;
            *)
                shift
                ;;
        esac
    done
    
    # Normalize symbol format
    if [[ -n "$TRADE_SYMBOL" ]]; then
        if [[ "$TRADE_SYMBOL" != *"/"* ]]; then
            local normalized_symbol=$(echo "${TRADE_SYMBOL}" | tr '[:lower:]' '[:upper:]')
            TRADE_SYMBOL="${normalized_symbol}/USDT:USDT"
            log_info "Normalized symbol: $TRADE_SYMBOL"
        fi
    fi
}

# Run Docker container for testing
run_docker_container() {
    local detach_mode="${1:-true}"
    local container_name="${2:-local-crypto-trading-bot}"
    
    log_info "Starting container in LOCAL mode: $container_name"
    
    # Remove existing container if it exists
    docker stop "$container_name" 2>/dev/null || true
    docker rm "$container_name" 2>/dev/null || true
    
    # Build docker run command
    local docker_cmd="docker run"
    
    if [[ "$detach_mode" == "true" ]]; then
        docker_cmd+=" -d"
    else
        docker_cmd+=" -it"
    fi
    
    docker_cmd+=" --name $container_name"
    
    # Local development: no auto-restart for easier debugging
    if [[ "$DEV_MODE" == "true" ]]; then
        log_debug "Development mode: no auto-restart"
    else
        docker_cmd+=" --restart unless-stopped"
    fi
    
    # Add environment variables
    docker_cmd+=" -e DEV_MODE=true"
    docker_cmd+=" -e LOCAL_BUILD=true"
    [[ -n "$TRADE_SYMBOL" ]] && docker_cmd+=" -e TRADE_SYMBOL='$TRADE_SYMBOL'"
    [[ -n "$TRADE_AMOUNT" ]] && docker_cmd+=" -e TRADE_AMOUNT='$TRADE_AMOUNT'"
    [[ -n "$TRADE_EXCHANGE" ]] && docker_cmd+=" -e TRADE_EXCHANGE='$TRADE_EXCHANGE'"
    [[ -n "$TRADE_DIRECTION" ]] && docker_cmd+=" -e TRADE_DIRECTION='$TRADE_DIRECTION'"
    [[ -n "$TRADE_TEST_MODE" ]] && docker_cmd+=" -e TRADE_TEST_MODE='$TRADE_TEST_MODE'"
    [[ -n "$TRADE_STOP_LOSS" ]] && docker_cmd+=" -e TRADE_STOP_LOSS='$TRADE_STOP_LOSS'"
    [[ -n "$TRADE_TAKE_PROFIT" ]] && docker_cmd+=" -e TRADE_TAKE_PROFIT='$TRADE_TAKE_PROFIT'"
    [[ -n "$BYBIT_API_KEY" ]] && docker_cmd+=" -e BYBIT_API_KEY='$BYBIT_API_KEY'"
    [[ -n "$BYBIT_API_SECRET" ]] && docker_cmd+=" -e BYBIT_API_SECRET='$BYBIT_API_SECRET'"
    [[ "$DEBUG_MODE" == "true" ]] && docker_cmd+=" -e LOG_LEVEL=DEBUG"
    
    # Mount local volumes for development
    docker_cmd+=" -v $(pwd)/logs:/app/logs"
    docker_cmd+=" -v $(pwd)/data:/app/data"
    docker_cmd+=" -v $(pwd)/configs:/app/configs:ro"
    
    docker_cmd+=" $TRADER_IMAGE"
    
    log_info "Starting container: $container_name"
    log_info "Image: $TRADER_IMAGE"
    log_debug "Command: $docker_cmd"
    
    if eval "$docker_cmd"; then
        log_success "Container started successfully: $container_name"
        send_telegram_notification "LOCAL trading bot started: $container_name" "start"
        return 0
    else
        log_error "Failed to start container: $container_name"
        return 1
    fi
}

# Start bot (with auto-build if needed)
start_bot() {
    log_info "🤖 Starting Trading Bot (LOCAL MODE)"
    echo "===================================="
    
    parse_trading_arguments "$@"
    
    if [[ -z "$TRADE_SYMBOL" ]]; then
        log_error "Symbol is required"
        echo "Usage: $0 start <symbol> [options]"
        return 1
    fi
    
    if ! check_docker; then
        return 1
    fi
    
    # Auto-rebuild if requested or image doesn't exist
    if [[ "$REBUILD_ON_START" == "true" ]] || ! docker images -q "$TRADER_IMAGE" &>/dev/null; then
        log_info "Building/rebuilding trader image..."
        build_trader_image "$REBUILD_ON_START"
    fi
    
    CONTAINER_NAME=$(generate_container_name "$TRADE_SYMBOL")
    
    echo ""
    log_info "📊 Trading Configuration:"
    echo "   Symbol: $TRADE_SYMBOL"
    echo "   Amount: ${TRADE_AMOUNT:-default}"
    echo "   Container: $CONTAINER_NAME"
    echo "   Test Mode: ${TRADE_TEST_MODE:-false}"
    echo "   Debug Mode: $DEBUG_MODE"
    echo "   Image: $TRADER_IMAGE"
    
    run_docker_container true "$CONTAINER_NAME"
}

# Stop bot
stop_bot() {
    local symbol="$1"
    
    if [[ -z "$symbol" ]]; then
        log_error "Symbol is required"
        echo "Usage: $0 stop <symbol>"
        return 1
    fi
    
    local container_name=$(generate_container_name "$symbol")
    
    log_info "⏹️ Stopping local trading bot: $container_name"
    
    if docker stop "$container_name" 2>/dev/null; then
        log_success "Container stopped: $container_name"
        send_telegram_notification "LOCAL trading bot stopped: $container_name"
    else
        log_warning "Container not running: $container_name"
    fi
    
    if docker rm "$container_name" 2>/dev/null; then
        log_success "Container removed: $container_name"
    fi
}

# Restart bot
restart_bot() {
    local symbol="$1"
    local rebuild="${2:-false}"
    
    if [[ -z "$symbol" ]]; then
        log_error "Symbol is required"
        echo "Usage: $0 restart <symbol> [--rebuild]"
        return 1
    fi
    
    log_info "🔄 Restarting local bot for symbol: $symbol"
    
    stop_bot "$symbol"
    sleep 2
    
    if [[ "$rebuild" == "true" ]]; then
        REBUILD_ON_START="true"
    fi
    
    start_bot "$symbol" --amount "${TRADE_AMOUNT:-50}"
}

# Start Telegram bot locally
start_telegram_bot() {
    log_info "📱 Starting Telegram Bot (LOCAL MODE)"
    echo "===================================="
    
    if ! check_telegram_available; then
        log_error "Telegram not configured properly"
        echo "Please set TELEGRAM_BOT_TOKEN and TELEGRAM_CHAT_ID"
        return 1
    fi
    
    # Auto-build if requested or image doesn't exist
    if [[ "$REBUILD_ON_START" == "true" ]] || ! docker images -q "$TELEGRAM_IMAGE" &>/dev/null; then
        log_info "Building/rebuilding telegram image..."
        build_telegram_image "$REBUILD_ON_START"
    fi
    
    # Stop existing telegram bot
    docker stop local-autotrader-telegram 2>/dev/null || true
    docker rm local-autotrader-telegram 2>/dev/null || true
    
    log_info "🚀 Starting local Telegram bot container..."
    docker run -d \
        --name local-autotrader-telegram \
        -e TELEGRAM_BOT_TOKEN="$TELEGRAM_BOT_TOKEN" \
        -e TELEGRAM_CHAT_ID="$TELEGRAM_CHAT_ID" \
        -e DEV_MODE=true \
        -e LOCAL_BUILD=true \
        -v /var/run/docker.sock:/var/run/docker.sock:ro \
        -v $(pwd)/logs:/app/logs \
        -v $(pwd)/data:/app/data \
        -v $(pwd)/configs:/app/configs:ro \
        -v $(pwd)/credentials:/app/credentials \
        "$TELEGRAM_IMAGE"
    
    log_success "Local Telegram bot started successfully"
    send_telegram_notification "🏠 LOCAL Telegram bot started and ready for testing!"
}

# List local containers
list_containers() {
    log_info "📊 Local Trading Bot Containers"
    echo "==============================="
    
    if ! check_docker; then
        return 1
    fi
    
    local containers=$(docker ps -a --format "table {{.Names}}\t{{.Status}}\t{{.Image}}\t{{.CreatedAt}}" | grep -E "(local-|autotrader-local)" || true)
    
    if [[ -z "$containers" ]]; then
        log_warning "📭 No local trading bot containers found"
        echo ""
        echo "💡 Start a bot with:"
        echo "   $0 start <symbol> --amount <amount>"
        return 0
    fi
    
    echo "$containers"
}

# Get logs with enhanced output for debugging
get_logs() {
    local symbol="$1"
    local lines="${2:-50}"
    local follow="${3:-false}"
    
    if [[ -z "$symbol" ]]; then
        log_error "Symbol is required"
        echo "Usage: $0 logs <symbol> [lines] [--follow]"
        return 1
    fi
    
    local container_name=$(generate_container_name "$symbol")
    
    if ! docker ps -a --format "{{.Names}}" | grep -q "^${container_name}$"; then
        log_error "No container found for symbol: $symbol"
        return 1
    fi
    
    log_info "📋 Last $lines lines for $symbol:"
    echo "==============================="
    
    if [[ "$follow" == "true" ]]; then
        docker logs --tail "$lines" -f "$container_name"
    else
        docker logs --tail "$lines" "$container_name"
    fi
}

# Debug container - interactive shell
debug_container() {
    local symbol="$1"
    
    if [[ -z "$symbol" ]]; then
        log_error "Symbol is required"
        echo "Usage: $0 debug <symbol>"
        return 1
    fi
    
    local container_name=$(generate_container_name "$symbol")
    
    if ! docker ps --format "{{.Names}}" | grep -q "^${container_name}$"; then
        log_error "Container is not running: $container_name"
        echo "Start it first with: $0 start $symbol"
        return 1
    fi
    
    log_info "🐛 Entering debug shell for: $container_name"
    docker exec -it "$container_name" /bin/bash
}

# Clean up local images and containers
cleanup() {
    local force="${1:-false}"
    
    log_info "🧹 Cleaning up local AutoTrader resources"
    
    if [[ "$force" != "true" ]]; then
        read -p "Are you sure you want to cleanup all local AutoTrader containers and images? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "Cleanup cancelled"
            return 0
        fi
    fi
    
    # Stop and remove containers
    log_info "Stopping and removing containers..."
    local containers=$(docker ps -a --format "{{.Names}}" | grep -E "(local-|autotrader-local)" || true)
    
    if [[ -n "$containers" ]]; then
        echo "$containers" | while read container; do
            log_info "  Removing: $container"
            docker stop "$container" 2>/dev/null || true
            docker rm "$container" 2>/dev/null || true
        done
    fi
    
    # Remove images
    log_info "Removing local images..."
    docker rmi "$TELEGRAM_IMAGE" 2>/dev/null || true
    docker rmi "$TRADER_IMAGE" 2>/dev/null || true
    
    # Clean up dangling images
    docker image prune -f
    
    log_success "Cleanup completed!"
}

# System status for local development
system_status() {
    log_info "📊 AutoTrader Local Development Status"
    echo "======================================"
    
    # Check local images
    echo ""
    log_info "🖼️ Local Images:"
    if docker images -q "$TELEGRAM_IMAGE" &>/dev/null; then
        local telegram_size=$(docker images --format "{{.Size}}" "$TELEGRAM_IMAGE" | head -1)
        echo "   Telegram: ✅ Built ($telegram_size)"
    else
        echo "   Telegram: ❌ Not built"
    fi
    
    if docker images -q "$TRADER_IMAGE" &>/dev/null; then
        local trader_size=$(docker images --format "{{.Size}}" "$TRADER_IMAGE" | head -1)
        echo "   Trader: ✅ Built ($trader_size)"
    else
        echo "   Trader: ❌ Not built"
    fi
    
    # Check containers
    echo ""
    log_info "🐳 Local Containers:"
    local telegram_running=$(docker ps --format "{{.Names}}" | grep "local-autotrader-telegram" || true)
    local trading_containers=$(docker ps --format "table {{.Names}}\t{{.Status}}" | grep -E "(local-)" | grep -v "local-autotrader-telegram" || true)
    
    if [[ -n "$telegram_running" ]]; then
        echo "   Telegram Bot: ✅ Running"
    else
        echo "   Telegram Bot: ❌ Stopped"
    fi
    
    if [[ -n "$trading_containers" ]]; then
        echo "   Trading Bots:"
        echo "$trading_containers" | sed 's/^/     /'
    else
        echo "   Trading Bots: 📭 None running"
    fi
    
    # Check development environment
    echo ""
    log_info "🔧 Development Environment:"
    echo "   Script Version: $SCRIPT_VERSION"
    echo "   Build Mode: $BUILD_MODE"
    echo "   Debug Mode: $DEBUG_MODE"
    echo "   Dev Mode: $DEV_MODE"
    echo "   Docker: $([[ $(check_docker) ]] && echo "✅ Available" || echo "❌ Not available")"
    echo "   Python venv: $([[ -f ".venv/bin/activate" ]] && echo "✅ Available" || echo "❌ Not found")"
    
    # Check Telegram config
    if [[ -n "$TELEGRAM_BOT_TOKEN" ]]; then
        echo "   Telegram Token: ✅ Set (${TELEGRAM_BOT_TOKEN:0:10}...)"
    else
        echo "   Telegram Token: ❌ Not set"
    fi
    
    if [[ -n "$TELEGRAM_CHAT_ID" ]]; then
        echo "   Chat ID: ✅ Set ($TELEGRAM_CHAT_ID)"
    else
        echo "   Chat ID: ❌ Not set"
    fi
}

# Quick test runner
run_tests() {
    log_info "🧪 Running Local AutoTrader Tests"
    echo "=================================="
    
    # Test 1: Build images
    log_info "Test 1: Building images..."
    if build_images both true; then
        log_success "✅ Image build test passed"
    else
        log_error "❌ Image build test failed"
        return 1
    fi
    
    # Test 2: Start and stop a test container
    log_info "Test 2: Container lifecycle test..."
    if start_bot "TESTCOIN" --amount 1 --test-mode; then
        sleep 5
        if stop_bot "TESTCOIN"; then
            log_success "✅ Container lifecycle test passed"
        else
            log_error "❌ Container stop test failed"
            return 1
        fi
    else
        log_error "❌ Container start test failed"
        return 1
    fi
    
    # Test 3: Telegram bot (if configured)
    if check_telegram_available; then
        log_info "Test 3: Telegram bot test..."
        if start_telegram_bot; then
            sleep 3
            if docker ps --format "{{.Names}}" | grep -q "local-autotrader-telegram"; then
                log_success "✅ Telegram bot test passed"
                docker stop local-autotrader-telegram &>/dev/null || true
                docker rm local-autotrader-telegram &>/dev/null || true
            else
                log_error "❌ Telegram bot test failed"
            fi
        fi
    else
        log_warning "⚠️ Skipping Telegram test (not configured)"
    fi
    
    log_success "🎉 All tests completed!"
}

# Show help
show_help() {
    cat << EOF
🤖 AutoTrader Local Development & Testing Script

USAGE:
    $0 <command> [options]

BUILD COMMANDS:
    build [mode]             Build Docker images (telegram|trader|both)
    rebuild [mode]           Force rebuild images
    
TESTING COMMANDS:
    start <symbol> [options] Start local trading bot
    stop <symbol>           Stop local trading bot  
    restart <symbol>        Restart local trading bot
    debug <symbol>          Enter interactive shell in container
    test                    Run comprehensive test suite

TELEGRAM COMMANDS:
    telegram                Start local Telegram bot
    
UTILITY COMMANDS:
    list                    List local containers
    status                  Show development environment status
    logs <symbol> [lines]   Show container logs
    cleanup [--force]       Clean up all local resources
    help                    Show this help

START OPTIONS:
    --amount <amount>       Trading amount (default: 50)
    --test-mode            Enable test mode
    --debug                Enable debug logging
    --rebuild              Rebuild image before starting
    --direction <long|short> Trading direction
    --stop-loss <percent>   Stop loss percentage
    --take-profit <percent> Take profit percentage
    --key <api_key>        API key
    --secret <api_secret>  API secret

EXAMPLES:
    # Build all images
    $0 build both
    
    # Start trading bot with debug
    $0 start hyper --amount 50 --debug --test-mode
    
    # Debug running container
    $0 debug hyper
    
    # Follow logs in real-time
    $0 logs hyper 100 --follow
    
    # Run comprehensive tests
    $0 test
    
    # Clean up everything
    $0 cleanup --force

DEVELOPMENT FEATURES:
    ✅ Local Docker image building
    ✅ Interactive debugging with shell access
    ✅ Enhanced logging and debug modes
    ✅ Comprehensive test suite
    ✅ Volume mounting for live development
    ✅ No auto-restart for easier debugging

For production deployment, use the main bot.sh script.

EOF
}

# Main function
main() {
    case "${1:-help}" in
        # Build commands
        build)
            build_images "$2" false
            ;;
        rebuild)
            build_images "$2" true
            ;;
        
        # Testing commands
        start)
            shift
            start_bot "$@"
            ;;
        stop)
            stop_bot "$2"
            ;;
        restart)
            if [[ "$3" == "--rebuild" ]]; then
                restart_bot "$2" true
            else
                restart_bot "$2" false
            fi
            ;;
        debug)
            debug_container "$2"
            ;;
        test)
            run_tests
            ;;
        
        # Telegram commands
        telegram)
            start_telegram_bot
            ;;
        
        # Utility commands
        list)
            list_containers
            ;;
        status)
            system_status
            ;;
        logs)
            if [[ "$4" == "--follow" ]]; then
                get_logs "$2" "$3" true
            else
                get_logs "$2" "$3" false
            fi
            ;;
        cleanup)
            if [[ "$2" == "--force" ]]; then
                cleanup true
            else
                cleanup false
            fi
            ;;
        
        # Help
        help|--help|-h)
            show_help
            ;;
        *)
            log_error "Unknown command: $1"
            echo ""
            echo "🔍 Common commands:"
            echo "  • $0 build both      - Build all images"
            echo "  • $0 start hyper     - Start trading bot"
            echo "  • $0 status         - Check environment"
            echo "  • $0 help           - Full help"
            exit 1
            ;;
    esac
}

# Execute main function
main "$@" 