#!/bin/bash

echo "🔄 Stopping existing container..."
docker stop autotrader-telegram 2>/dev/null || true
docker rm autotrader-telegram 2>/dev/null || true

echo "🏗️ Building image..."
docker build -t autotrader-telegram -f Dockerfile.telegram .

echo "🚀 Starting container with Docker socket access..."
docker run -d \
    --name autotrader-telegram \
    --env-file .env \
    -v $(pwd):/app \
    -v /var/run/docker.sock:/var/run/docker.sock \
    --privileged \
    autotrader-telegram

echo "📋 Container status:"
docker ps --filter name=autotrader-telegram --format "table {{.Names}}\t{{.Status}}"

echo "📜 Container logs:"
sleep 3
docker logs autotrader-telegram --tail 15

echo ""
echo "🧪 Testing bot.sh access:"
docker exec autotrader-telegram ls -la /app/bot.sh
docker exec autotrader-telegram /app/bot.sh version
