#!/bin/bash
# Updated Central Telegram Bot Manager Script
# Now uses unified Python Telegram application

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${GREEN}🎛️ Central Telegram Bot Manager (v3.0)${NC}"
echo "=========================================="
echo ""

# Check if telegram credentials are set
if [ -z "$TELEGRAM_BOT_TOKEN" ] || [ -z "$TELEGRAM_CHAT_ID" ]; then
    echo -e "${RED}❌ Telegram credentials not found!${NC}"
    echo ""
    echo "Please set environment variables:"
    echo "export TELEGRAM_BOT_TOKEN='your_bot_token'"
    echo "export TELEGRAM_CHAT_ID='your_chat_id'"
    echo ""
    echo "📖 See TELEGRAM_SETUP.md for instructions"
    exit 1
fi

echo -e "${GREEN}✅ Telegram credentials found${NC}"
echo "   Bot Token: ${TELEGRAM_BOT_TOKEN:0:10}..."
echo "   Chat ID: $TELEGRAM_CHAT_ID"
echo ""

# Check Python
if ! command -v python3 &> /dev/null; then
    echo -e "${RED}❌ Python 3 is not installed${NC}"
    exit 1
fi

# Check if unified Telegram app exists
if [ ! -f "run_telegram_app.py" ]; then
    echo -e "${RED}❌ Unified Telegram app (run_telegram_app.py) not found${NC}"
    echo "Please ensure the new Python application is available"
    exit 1
fi

echo -e "${GREEN}✅ Python and Telegram app available${NC}"
echo ""

# Check Docker
if ! command -v docker &> /dev/null; then
    echo -e "${RED}❌ Docker is not installed${NC}"
    exit 1
fi

if ! docker info >/dev/null 2>&1; then
    echo -e "${RED}❌ Docker daemon is not running${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Docker is available${NC}"
echo ""

# Install dependencies if needed
install_dependencies() {
    echo -e "${YELLOW}📦 Checking dependencies...${NC}"
    
    local missing_deps=()
    
    if ! python3 -c "import telegram" 2>/dev/null; then
        missing_deps+=("python-telegram-bot==20.7")
    fi
    
    if ! python3 -c "import aiohttp" 2>/dev/null; then
        missing_deps+=("aiohttp")
    fi
    
    if ! python3 -c "import docker" 2>/dev/null; then
        missing_deps+=("docker==7.0.0")
    fi
    
    if [ ${#missing_deps[@]} -gt 0 ]; then
        echo -e "${YELLOW}Installing missing dependencies: ${missing_deps[*]}${NC}"
        pip install "${missing_deps[@]}"
        echo -e "${GREEN}✅ Dependencies installed${NC}"
    else
        echo -e "${GREEN}✅ All dependencies already installed${NC}"
    fi
    echo ""
}

# Command handling
case "${1:-start}" in
    start)
        echo -e "${YELLOW}🚀 Starting Unified Telegram Manager...${NC}"
        echo ""
        
        # Check if already running
        if pgrep -f "run_telegram_app.py start" > /dev/null; then
            echo -e "${RED}❌ Telegram Manager is already running${NC}"
            echo "Use './telegram_central.sh stop' to stop it first"
            exit 1
        fi
        
        # Install dependencies
        install_dependencies
        
        # Start the unified Telegram app
        echo -e "${BLUE}🔧 Starting unified Telegram application...${NC}"
        if [ -f ".venv/bin/activate" ]; then
            source .venv/bin/activate
        fi
        nohup python run_telegram_app.py start \
            --token "$TELEGRAM_BOT_TOKEN" \
            --chat-id "$TELEGRAM_CHAT_ID" > telegram_central.log 2>&1 &
        
        PID=$!
        echo $PID > telegram_central.pid
        
        sleep 3
        
        if ps -p $PID > /dev/null; then
            echo -e "${GREEN}✅ Unified Telegram Manager started (PID: $PID)${NC}"
            echo ""
            echo -e "${BLUE}📋 Enhanced Commands available in Telegram:${NC}"
            echo ""
            echo -e "${GREEN}🔑 Credential Management:${NC}"
            echo "  /addcreds - Add new API credentials (wizard)"
            echo "  /listcreds - List stored credentials"
            echo "  /loadcreds [profile] - Load credentials"
            echo "  /setkey <key> <secret> - Quick credential setup"
            echo ""
            echo -e "${GREEN}🤖 Bot Management:${NC}"
            echo "  /createbot - Create new trading bot (wizard)"
            echo "  /startbot <symbol> <amount> - Quick start bot"
            echo "  /list - List all trading bots"
            echo "  /status [symbol] - Get bot status"
            echo "  /logs <symbol> [lines] - View bot logs"
            echo "  /stop <symbol> - Stop bot"
            echo "  /restart <symbol> - Restart bot"
            echo ""
            echo -e "${GREEN}📊 Information:${NC}"
            echo "  /help - Show all commands with interactive buttons"
            echo "  /start - Welcome message and quick setup"
            echo ""
            echo -e "${BLUE}✨ New Features:${NC}"
            echo "  • Interactive wizards for bot creation"
            echo "  • HTML-formatted messages (stable display)"
            echo "  • Inline keyboards for better UX"
            echo "  • Session management for multi-step operations"
            echo "  • AES-256 encrypted credential storage"
            echo "  • Template-based message system"
            echo ""
            echo "📋 View logs: tail -f telegram_central.log"
        else
            echo -e "${RED}❌ Failed to start Telegram Manager${NC}"
            echo "Check telegram_central.log for errors"
            exit 1
        fi
        ;;
        
    stop)
        echo -e "${YELLOW}🛑 Stopping Telegram Manager...${NC}"
        
        if [ -f telegram_central.pid ]; then
            PID=$(cat telegram_central.pid)
            if ps -p $PID > /dev/null; then
                kill $PID
                rm telegram_central.pid
                echo -e "${GREEN}✅ Telegram Manager stopped${NC}"
            else
                echo -e "${YELLOW}⚠️ Process not found, cleaning up pid file${NC}"
                rm telegram_central.pid
            fi
        else
            # Try to find by process name
            PID=$(pgrep -f "run_telegram_app.py start")
            if [ -n "$PID" ]; then
                kill $PID
                echo -e "${GREEN}✅ Telegram Manager stopped${NC}"
            else
                echo -e "${YELLOW}⚠️ Telegram Manager is not running${NC}"
            fi
        fi
        ;;
        
    restart)
        echo -e "${YELLOW}🔄 Restarting Telegram Manager...${NC}"
        $0 stop
        sleep 3
        $0 start
        ;;
        
    status)
        echo -e "${YELLOW}📊 Telegram Manager Status${NC}"
        echo ""
        
        if [ -f telegram_central.pid ]; then
            PID=$(cat telegram_central.pid)
            if ps -p $PID > /dev/null; then
                echo -e "${GREEN}✅ Running (PID: $PID)${NC}"
                echo ""
                
                # Show enhanced status
                echo -e "${BLUE}📊 System Information:${NC}"
                echo "  • Process: Unified Python Telegram App"
                echo "  • Version: 3.0 (Restructured Architecture)"
                echo "  • Features: Templates, Wizards, HTML formatting"
                echo "  • Session Manager: Active"
                echo ""
                
                # Show recent logs
                echo -e "${BLUE}📋 Recent logs:${NC}"
                tail -n 10 telegram_central.log
            else
                echo -e "${RED}❌ Not running (stale pid file)${NC}"
                rm telegram_central.pid
            fi
        else
            PID=$(pgrep -f "run_telegram_app.py start")
            if [ -n "$PID" ]; then
                echo -e "${GREEN}✅ Running (PID: $PID)${NC}"
                echo $PID > telegram_central.pid
            else
                echo -e "${RED}❌ Not running${NC}"
            fi
        fi
        ;;
        
    logs)
        echo -e "${YELLOW}📋 Telegram Manager Logs${NC}"
        echo ""
        if [ -f telegram_central.log ]; then
            tail -f telegram_central.log
        else
            echo -e "${RED}❌ Log file not found${NC}"
        fi
        ;;
        
    test)
        echo -e "${YELLOW}🧪 Testing Telegram Integration${NC}"
        echo ""
        
        # Send test message using new Python app with venv
        echo "Sending test message..."
        if [ -f ".venv/bin/activate" ]; then
            source .venv/bin/activate
        fi
        python run_telegram_app.py send \
            --token "$TELEGRAM_BOT_TOKEN" \
            --chat-id "$TELEGRAM_CHAT_ID" \
            --message "🧪 Test message from updated Telegram Manager v3.0

✨ New features active:
• Template-based messaging
• HTML formatting
• Improved error handling
• Python-based architecture

Ready for enhanced trading bot management!" 2>&1
        
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✅ Test message sent successfully${NC}"
        else
            echo -e "${RED}❌ Failed to send test message${NC}"
        fi
        ;;
        
    upgrade)
        echo -e "${YELLOW}⬆️ Upgrading to New Architecture${NC}"
        echo ""
        
        echo "🔄 Stopping old version..."
        $0 stop
        
        echo "📦 Installing/updating dependencies..."
        install_dependencies
        
        echo "✅ Architecture upgraded to v3.0"
        echo ""
        echo "🚀 Starting new version..."
        $0 start
        ;;
        
    info)
        echo -e "${BLUE}ℹ️ Telegram Manager Information${NC}"
        echo ""
        echo -e "${GREEN}📊 Architecture Overview:${NC}"
        echo "  • CLI Interface: Simplified bot.sh (CLI only)"
        echo "  • Telegram Logic: Python modules in src/infrastructure/telegram/"
        echo "  • Templates: Centralized in templates.py"
        echo "  • API Client: Modern aiohttp-based client"
        echo "  • Message Formatting: HTML-based (stable)"
        echo "  • Session Management: In-memory with cleanup"
        echo "  • Wizards: Multi-step interactive flows"
        echo ""
        echo -e "${GREEN}🗂️ Key Files:${NC}"
        echo "  • run_telegram_app.py - Unified entry point"
        echo "  • src/infrastructure/telegram/templates.py - Message templates"
        echo "  • src/infrastructure/telegram/telegram_api_client.py - API client"
        echo "  • src/infrastructure/telegram/handlers/ - Command handlers"
        echo ""
        echo -e "${GREEN}🔧 Configuration:${NC}"
        echo "  • TELEGRAM_BOT_TOKEN: ${TELEGRAM_BOT_TOKEN:0:10}..."
        echo "  • TELEGRAM_CHAT_ID: $TELEGRAM_CHAT_ID"
        echo "  • Version: 3.0.0"
        ;;
        
    *)
        echo "Usage: $0 {start|stop|restart|status|logs|test|upgrade|info}"
        echo ""
        echo -e "${GREEN}Commands:${NC}"
        echo "  start   - Start Unified Telegram Manager"
        echo "  stop    - Stop Telegram Manager"
        echo "  restart - Restart Telegram Manager"
        echo "  status  - Show detailed status"
        echo "  logs    - Show real-time logs"
        echo "  test    - Send test message"
        echo "  upgrade - Upgrade from old architecture"
        echo "  info    - Show architecture information"
        echo ""
        echo -e "${BLUE}✨ New in v3.0:${NC}"
        echo "  • Complete Python-based Telegram handling"
        echo "  • Template system for consistent messaging"
        echo "  • Interactive wizards and better UX"
        echo "  • Improved error handling and stability"
        exit 1
        ;;
esac 