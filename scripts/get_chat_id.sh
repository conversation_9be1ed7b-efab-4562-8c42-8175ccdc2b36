#!/bin/bash

# Script để lấy Chat ID từ Telegram Bot

echo "🔍 Đang lấy Chat ID từ Telegram Bot..."
echo "Bot Token: ${TELEGRAM_BOT_TOKEN:0:15}..."
echo ""

if [ -z "$TELEGRAM_BOT_TOKEN" ]; then
    echo "❌ TELEGRAM_BOT_TOKEN chưa được set!"
    echo "Chạy: export TELEGRAM_BOT_TOKEN='your_token_here'"
    exit 1
fi

# Get updates từ Telegram
response=$(curl -s "https://api.telegram.org/bot${TELEGRAM_BOT_TOKEN}/getUpdates")

# Parse và hiển thị chat IDs
echo "📱 Các tin nhắn gần đây:"
echo "$response" | python3 -c "
import sys
import json

data = json.load(sys.stdin)

if not data['ok']:
    print('❌ Lỗi API:', data.get('description', 'Unknown error'))
    sys.exit(1)

results = data['result']

if not results:
    print('📭 Chưa có tin nhắn nào!')
    print('')
    print('📱 Hãy:')
    print('1. Mở Telegram')
    print('2. Tìm bot của bạn')
    print('3. Gửi tin nhắn /start')
    print('4. Chạy lại script này')
    sys.exit(0)

print(f'✅ Tìm thấy {len(results)} tin nhắn:')
print('')

chat_ids = set()
for msg in results:
    if 'message' in msg:
        chat = msg['message']['chat']
        chat_id = chat['id']
        chat_type = chat['type']
        name = chat.get('first_name', chat.get('title', 'Unknown'))
        
        chat_ids.add(chat_id)
        
        print(f'💬 Chat ID: {chat_id}')
        print(f'   Type: {chat_type}')
        print(f'   Name: {name}')
        print(f'   Text: {msg[\"message\"].get(\"text\", \"(no text)\")}')
        print('')

if chat_ids:
    main_chat_id = list(chat_ids)[0]  # Lấy chat ID đầu tiên
    print(f'🎯 Sử dụng Chat ID chính: {main_chat_id}')
    print('')
    print('📋 Chạy lệnh này để set Chat ID:')
    print(f'export TELEGRAM_CHAT_ID=\"{main_chat_id}\"')
" 