#!/bin/bash

# Setup script for autotrader-telegram container
# This script installs Docker CLI and configures sudo permissions

echo "🔧 Setting up AutoTrader Telegram container..."

# Update package index
echo "📦 Updating package index..."
apk update

# Install Docker CLI and sudo
echo "🐳 Installing Docker CLI and sudo..."
apk add docker-cli sudo

# Configure sudo permissions for telegram user
echo "🔐 Configuring sudo permissions..."
echo "telegram ALL=(ALL) NOPASSWD: /usr/bin/docker" >> /etc/sudoers

# Create docker group and add telegram user
echo "👥 Configuring Docker group..."
addgroup docker 2>/dev/null || true
adduser telegram docker 2>/dev/null || true

# Create credentials directory for telegram user (unified to .autotrader)
echo "📁 Creating credentials directory..."
sudo -u telegram mkdir -p /home/<USER>/.autotrader/credentials
sudo -u telegram mkdir -p /root/.autotrader/credentials

echo "✅ Container setup completed!"
echo ""
echo "🧪 Testing Docker access..."
if sudo -u telegram sudo docker version >/dev/null 2>&1; then
    echo "✅ Docker CLI working correctly"
else
    echo "❌ Docker CLI setup failed"
    exit 1
fi

echo ""
echo "🚀 Container is ready for use!"
