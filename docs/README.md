# Documentation

This directory contains all project documentation files:

## Files

- `CONTAINER_NAMING_FIX.md` - Container naming fixes documentation
- `DOCKER_2_IMAGE_SETUP.md` - Docker image setup guide
- `PR_INSUFFICIENT_BALANCE_FIX.md` - Pull request for balance fix
- `README_SECURITY.md` - Security documentation
- `RESTRUCTURE_SUMMARY.md` - Project restructure summary
- `SETUP_GIST_SYNC.md` - Gist synchronization setup
- `TELEGRAM_SETUP.md` - Telegram bot setup guide
- `TP_SL_STRATEGY.md` - Take Profit/Stop Loss strategy documentation
- `bug_fixes_implemented.md` - Implemented bug fixes
- `quick_fix_summary.md` - Quick fixes summary
- `trading_bot_error_analysis.md` - Trading bot error analysis

## Usage

Refer to these documents for understanding the project architecture, setup procedures, and troubleshooting guides.
