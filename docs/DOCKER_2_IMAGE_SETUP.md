# 🐳 AutoTrader 2-Docker Architecture Setup

## 📋 Overview

AutoTrader now uses a **2-Docker image architecture** for better separation of concerns, security, and scalability:

1. **autotrader-telegram**: Telegram bot manager for centralized control
2. **autotrader-trader**: Individual trading bot instances

```
┌─────────────────────────────────────────────────────────────┐
│                    TELEGRAM BOT MANAGER                     │
│              (autotrader-telegram:latest)                   │
│                                                             │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │  🔑 Credentials │  │  🎛️ Bot Control │  │  🐳 Docker   │ │
│  │   Management    │  │    Wizards      │  │     API      │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
                              │ Docker API
                              ▼
   ┌──────────────────┐  ┌──────────────────┐  ┌──────────────────┐
   │ Trading Bot #1   │  │ Trading Bot #2   │  │ Trading Bot #N   │
   │ HYPER/USDT:USDT  │  │ BTC/USDT:USDT    │  │ ETH/USDT:USDT    │
   │ (trader-hyper)   │  │ (trader-btc)     │  │ (trader-eth)     │
   └──────────────────┘  └──────────────────┘  └──────────────────┘
```

## 🚀 Quick Start

### Prerequisites

1. **Docker & Docker Compose** installed
2. **Telegram Bot Token** (from @BotFather)
3. **Telegram Chat ID** (from @userinfobot)

### 1. Clone & Build

```bash
# Clone repository
git clone <your-repo-url>
cd autotrader

# Build both images
docker build -f Dockerfile.telegram -t autotrader-telegram:latest .
docker build -f Dockerfile.trader -t autotrader-trader:latest .
```

### 2. Environment Setup

```bash
# Create .env file
cat > .env << EOF
TELEGRAM_BOT_TOKEN=your_bot_token_here
TELEGRAM_CHAT_ID=your_chat_id_here
LOG_LEVEL=INFO
EOF

# Create required directories
mkdir -p configs credentials data logs docker_volumes
```

### 3. Start Telegram Manager

```bash
# Option 1: Docker Compose (Recommended)
docker-compose up -d telegram-manager

# Option 2: Direct Docker
docker run -d \
  --name autotrader-telegram \
  -e TELEGRAM_BOT_TOKEN="your_token" \
  -e TELEGRAM_CHAT_ID="your_chat_id" \
  -v $(pwd)/configs:/app/configs \
  -v $(pwd)/credentials:/app/credentials \
  -v $(pwd)/data:/app/data \
  -v $(pwd)/logs:/app/logs \
  -v /var/run/docker.sock:/var/run/docker.sock:ro \
  -p 8081:8080 \
  autotrader-telegram:latest
```

## 📱 Telegram Commands

### 🔑 Credential Management
```
/addcreds     - Add API credentials wizard (4 steps)
/listcreds    - List all credential profiles  
/showcreds    - Show credential details
/selectcreds  - Choose credential profile
/deletecreds  - Delete credential profile
```

### 🤖 Bot Management
```
/createbot    - Create new trading bot (7-step wizard)
/startbot     - Quick start: /startbot HYPER/USDT:USDT 10
/list         - List all trading containers
/status       - Show container status
/logs         - View container logs
/stop         - Stop specific container
/restart      - Restart container
/stopall      - Stop all trading containers
```

### 🔧 System Commands
```
/start        - Welcome message with quick actions
/help         - Show all commands
/cancel       - Cancel active wizard
```

## 🎯 Usage Examples

### 1. Setup Credentials

1. Type `/addcreds` in Telegram
2. Follow the 4-step wizard:
   - **Step 1**: Profile name (e.g., "main")
   - **Step 2**: API Key
   - **Step 3**: API Secret  
   - **Step 4**: Display name (optional)

### 2. Create Trading Bot

**Option A: Wizard (Recommended)**
```
/createbot
```
Follow 7-step wizard for complete configuration.

**Option B: Quick Start**
```
/startbot HYPER/USDT:USDT 10
/startbot BTC/USDT:USDT 50 --test-mode
/startbot ETH/USDT:USDT 100 --stop-loss=5 --take-profit=10
```

### 3. Monitor & Control

```
/list           # See all your trading bots
/status         # System overview  
/logs trader-1  # View specific bot logs
/stop trader-1  # Stop specific bot
/restart trader-1 # Restart bot
/stopall        # Emergency stop all
```

## 🛡️ Security Features

### 🔐 AES-256 Credential Encryption
- All API credentials encrypted with AES-256-CBC
- Unique salt per credential file
- Auto-generated master key
- Secure file permissions (600/700)

### 👤 User Authentication
- Telegram Chat ID authorization
- Only authorized chat can control bots
- Command validation and sanitization

### 🐳 Container Security
- Non-root users in containers
- Read-only config mounts
- Isolated network namespace
- Resource limits

## 🏗️ Architecture Details

### Directory Structure
```
autotrader/
├── Dockerfile.telegram          # Telegram bot image
├── Dockerfile.trader           # Trading bot image  
├── docker-compose.yml          # Orchestration
├── bot.sh                      # Consolidated management script
├── configs/                    # Trading configurations
├── credentials/                # Encrypted API credentials
├── data/                       # Trading data & state
├── logs/                       # Application logs
└── docker_volumes/             # Container shared data
```

### Communication Flow
```
Telegram → telegram-manager → Docker API → trader containers
     ↑           ↓                ↓              ↓
   User      Shared Volumes   Container Mgmt   Trading
```

### Volume Mounts
- **configs/**: Read-only trading configurations
- **credentials/**: Encrypted API credentials  
- **data/**: Persistent trading data
- **logs/**: Application logs
- **docker_volumes/**: Inter-container communication

## 📊 Monitoring & Health Checks

### Container Health
```bash
# Check telegram manager
docker logs autotrader-telegram

# Check specific trader
docker logs trader-hyper

# System overview
docker ps --filter "name=autotrader*"
```

### Health Endpoints
- **Telegram Manager**: http://localhost:8081
- **Trading Bots**: Dynamic ports (8080+)

### Log Locations
```bash
# Real-time logs
docker logs -f autotrader-telegram

# Persistent logs  
tail -f logs/telegram.log
tail -f logs/trader-*.log
```

## 🔧 Advanced Configuration

### Environment Variables

**Telegram Manager:**
```bash
TELEGRAM_BOT_TOKEN=required
TELEGRAM_CHAT_ID=required  
BOT_MODE=production|development
LOG_LEVEL=DEBUG|INFO|WARNING|ERROR
TELEGRAM_TIMEOUT=30
MAX_RETRIES=5
HEARTBEAT_INTERVAL=300
```

**Trading Bots:**
```bash
EXCHANGE=bybit
TRADE_MODE=test|live
TRADE_SYMBOL=HYPER/USDT:USDT
TRADE_AMOUNT=10
TRADE_DIRECTION=long|short|both
STOP_LOSS=5.0
TAKE_PROFIT=10.0
LOG_LEVEL=INFO
```

### Custom Docker Networks
```bash
# Create dedicated network
docker network create autotrader-net

# Run with custom network
docker run --network autotrader-net ...
```

## 🚨 Troubleshooting

### Common Issues

**1. Telegram Bot Not Responding**
```bash
# Check bot token
docker logs autotrader-telegram | grep "Bot Token"

# Verify API connection
curl "https://api.telegram.org/bot<TOKEN>/getMe"
```

**2. Docker Socket Permission Denied**
```bash
# Add user to docker group
sudo usermod -aG docker $USER
newgrp docker

# Or run with sudo
sudo docker-compose up -d
```

**3. Container Creation Failed**
```bash
# Check image availability
docker images | grep autotrader

# Check network connectivity
docker network ls
```

**4. Credential Loading Failed**
```bash
# Check credential files
ls -la credentials/
./bot.sh creds list

# Test decryption
./bot.sh creds show default
```

### Debug Mode
```bash
# Enable debug logging
export LOG_LEVEL=DEBUG

# Run telegram manager with debug
docker run -e LOG_LEVEL=DEBUG autotrader-telegram:latest
```

## 🔄 Updates & Maintenance

### Update Images
```bash
# Pull latest code
git pull origin main

# Rebuild images  
docker build -f Dockerfile.telegram -t autotrader-telegram:latest .
docker build -f Dockerfile.trader -t autotrader-trader:latest .

# Restart services
docker-compose down
docker-compose up -d telegram-manager
```

### Backup & Restore
```bash
# Backup credentials & data
tar -czf autotrader-backup.tar.gz credentials/ data/ configs/

# Restore
tar -xzf autotrader-backup.tar.gz
```

### Log Rotation
```bash
# Setup logrotate for container logs
sudo logrotate -f /etc/logrotate.d/docker

# Manual cleanup
docker system prune -f
```

## 📈 Production Deployment

### Docker Compose Production
```yaml
# docker-compose.prod.yml
version: "3.8"
services:
  telegram-manager:
    image: autotrader-telegram:v1.0.0
    restart: always
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: "0.5"
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "5"
```

### GitHub Actions CI/CD
- Automated image building
- Security scanning with Trivy
- Multi-architecture builds (amd64/arm64)
- Container registry publishing

### Monitoring Setup
```bash
# Prometheus metrics
curl http://localhost:8081/metrics

# Health checks
curl http://localhost:8081/health
```

## 🆘 Support

- **Issues**: Create GitHub issue with logs
- **Documentation**: Check inline help with `/help`  
- **Logs**: Always include container logs for debugging
- **Telegram**: Test with `/start` command first

## 📄 License

MIT License - see LICENSE file for details.

---

**🎉 Happy Trading!** Your AutoTrader 2-Docker architecture is ready for scalable, secure, and centralized crypto trading management via Telegram! 