# 🔐 Security Configuration Guide

This guide explains how to securely configure credentials for the AutoTrader bot.

## 🚨 Important Security Notice

**NEVER commit credentials to version control!** All sensitive information should be stored in environment variables or secure configuration files that are excluded from git.

## 📋 Required Environment Variables

### Telegram Bot Configuration
```bash
# Get from @BotFather on Telegram
export TELEGRAM_BOT_TOKEN="your_bot_token_here"

# Your Telegram user ID or group chat ID
export TELEGRAM_CHAT_ID="your_chat_id_here"
```

### Trading API Configuration
```bash
# Bybit API credentials
export BYBIT_API_KEY="your_bybit_api_key_here"
export BYBIT_API_SECRET="your_bybit_api_secret_here"

# Optional: Testnet credentials for testing
export BYBIT_TESTNET_API_KEY="your_testnet_api_key_here"
export BYBIT_TESTNET_API_SECRET="your_testnet_api_secret_here"
```

## 🛠️ Setup Methods

### Method 1: Environment Variables (Recommended)

1. **Create a `.env` file** (copy from `.env.example`):
```bash
cp .env.example .env
```

2. **Edit `.env` file** with your actual credentials:
```bash
nano .env
```

3. **Load environment variables**:
```bash
source .env
```

### Method 2: Export Commands

```bash
# Set environment variables for current session
export TELEGRAM_BOT_TOKEN="your_actual_bot_token"
export TELEGRAM_CHAT_ID="your_actual_chat_id"
export BYBIT_API_KEY="your_actual_api_key"
export BYBIT_API_SECRET="your_actual_api_secret"
```

### Method 3: Docker Environment

```bash
# Using docker run with environment variables
docker run -d \
  -e TELEGRAM_BOT_TOKEN="your_bot_token" \
  -e TELEGRAM_CHAT_ID="your_chat_id" \
  -e BYBIT_API_KEY="your_api_key" \
  -e BYBIT_API_SECRET="your_api_secret" \
  autotrader-telegram:latest
```

## 🔍 How to Get Credentials

### Telegram Bot Token
1. Message @BotFather on Telegram
2. Send `/newbot` command
3. Follow instructions to create your bot
4. Copy the token provided

### Telegram Chat ID
1. Message your bot or add it to a group
2. Visit: `https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getUpdates`
3. Look for `"chat":{"id":*********}` in the response
4. Use that ID as your TELEGRAM_CHAT_ID

### Bybit API Credentials
1. Login to Bybit account
2. Go to Account & Security → API Management
3. Create new API key with trading permissions
4. Copy API Key and Secret

## ✅ Verification

Test your configuration:

```bash
# Test Telegram bot
python3 verify_bot.py

# Test trading bot
python3 test_auto_telegram.py
```

## 🚫 What NOT to Do

❌ **Never do this:**
```python
# DON'T hardcode credentials in source code
TOKEN = "123456:ABC-DEF1234ghIkl-zyx57W2v1u123ew11"
CHAT_ID = *********
```

✅ **Always do this:**
```python
# DO use environment variables
TOKEN = os.getenv('TELEGRAM_BOT_TOKEN')
CHAT_ID = os.getenv('TELEGRAM_CHAT_ID')

if not TOKEN or not CHAT_ID:
    print("❌ Error: Required environment variables not set")
    sys.exit(1)
```

## 🔒 Security Best Practices

1. **Use `.env` files** for local development
2. **Set environment variables** in production
3. **Never commit** `.env` files to git
4. **Use different credentials** for testing and production
5. **Regularly rotate** API keys
6. **Limit API permissions** to only what's needed
7. **Monitor API usage** for suspicious activity

## 🆘 If Credentials Are Compromised

1. **Immediately revoke** the compromised API keys
2. **Generate new credentials**
3. **Update environment variables**
4. **Check logs** for unauthorized usage
5. **Consider changing** Telegram bot token

## 📁 File Structure

```
autotrader/
├── .env.example          # Template for environment variables
├── .env                  # Your actual credentials (gitignored)
├── .gitignore           # Ensures .env is not committed
├── README_SECURITY.md   # This security guide
└── src/
    └── ...
```

## 🔧 Troubleshooting

### "Environment variable not set" errors
- Check if `.env` file exists and has correct values
- Ensure you've run `source .env` or restarted your terminal
- Verify variable names match exactly (case-sensitive)

### "Invalid token" errors
- Double-check token format from @BotFather
- Ensure no extra spaces or characters
- Try creating a new bot token

### "Chat not found" errors
- Verify chat ID is correct number
- Ensure bot has been added to the chat/group
- For groups, use negative chat ID

Remember: **Security is everyone's responsibility!** 🛡️
