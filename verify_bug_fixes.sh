#!/bin/bash
# Comprehensive test script to verify all bug fixes

set -e

echo "🔧 Verifying Bug Fixes"
echo "======================"

# Source shell constants for testing
source src/core/shell_constants.sh

all_passed=true

echo ""
echo "🐛 Bug Fix 1: Symbol Normalization and Container Naming"
echo "------------------------------------------------------"

# Test cases that were previously broken
test_cases=(
    "ethusdt|ETH/USDT:USDT|ethusdt"
    "ETHUSDT|ETH/USDT:USDT|ethusdt"
    "btcusdt|BTC/USDT:USDT|btcusdt"
    "BTCUSDT|BTC/USDT:USDT|btcusdt"
    "solusdt|SOL/USDT:USDT|solusdt"
    "SOLUSDT|SOL/USDT:USDT|solusdt"
    "hyperusdt|HYPER/USDT:USDT|hyperusdt"
    "HYPERUSDT|HYPER/USDT:USDT|hyperusdt"
    "btc|BTC/USDT:USDT|btcusdt"
    "ETH|ETH/USDT:USDT|ethusdt"
    "BTC/USDT:USDT|BTC/USDT:USDT|btcusdt"
)

for test_case in "${test_cases[@]}"; do
    input=$(echo "$test_case" | cut -d'|' -f1)
    expected_normalized=$(echo "$test_case" | cut -d'|' -f2)
    expected_container=$(echo "$test_case" | cut -d'|' -f3)
    
    # Test normalize_symbol
    actual_normalized=$(normalize_symbol "$input")
    if [[ "$actual_normalized" == "$expected_normalized" ]]; then
        echo "✅ normalize_symbol('$input') = '$actual_normalized'"
    else
        echo "❌ normalize_symbol('$input') = '$actual_normalized' (expected: '$expected_normalized')"
        all_passed=false
    fi
    
    # Test get_container_name
    actual_container=$(get_container_name "$input")
    if [[ "$actual_container" == "$expected_container" ]]; then
        echo "✅ get_container_name('$input') = '$actual_container'"
    else
        echo "❌ get_container_name('$input') = '$actual_container' (expected: '$expected_container')"
        all_passed=false
    fi
done

# Check for invalid patterns
echo ""
echo "🔍 Checking for invalid patterns:"
invalid_patterns_found=false

for symbol in "ethusdt" "BTCUSDT" "solusdt"; do
    normalized=$(normalize_symbol "$symbol")
    container=$(get_container_name "$symbol")
    
    # Check for USDT/USDT:USDT pattern (invalid)
    if [[ "$normalized" =~ USDT/USDT:USDT ]]; then
        echo "❌ Invalid pattern found: '$symbol' → '$normalized' contains USDT/USDT:USDT"
        invalid_patterns_found=true
        all_passed=false
    fi
    
    # Check for duplicate usdt in container name
    if [[ "$container" =~ usdtusdt ]]; then
        echo "❌ Invalid container name: '$symbol' → '$container' contains duplicate usdt"
        invalid_patterns_found=true
        all_passed=false
    fi
done

if [[ "$invalid_patterns_found" == "false" ]]; then
    echo "✅ No invalid patterns found"
fi

echo ""
echo "🐛 Bug Fix 2: Return Code Handling"
echo "----------------------------------"

# Test successful command
echo "Testing successful command (./bot.sh help):"
if ./bot.sh help >/dev/null 2>&1; then
    echo "✅ Successful command returns exit code 0"
else
    echo "❌ Successful command returns non-zero exit code"
    all_passed=false
fi

# Test failing command
echo "Testing failing command (./bot.sh invalid_command):"
if ./bot.sh invalid_command >/dev/null 2>&1; then
    echo "❌ Invalid command returns exit code 0 (should be non-zero)"
    all_passed=false
else
    echo "✅ Invalid command returns non-zero exit code"
fi

# Test list command (should succeed)
echo "Testing list command:"
if ./bot.sh list >/dev/null 2>&1; then
    echo "✅ List command returns exit code 0"
else
    echo "❌ List command returns non-zero exit code"
    all_passed=false
fi

echo ""
echo "🧪 Integration Tests"
echo "-------------------"

# Test end-to-end symbol processing
echo "Testing end-to-end symbol processing:"

# Clean up any existing test containers
docker rm -f ethusdt 2>/dev/null || true

# Test with ethusdt (should normalize to ETH/USDT:USDT and create ethusdt container)
echo "Testing: ./bot.sh start ethusdt --amount 10 --test-mode"
if ./bot.sh start ethusdt --amount 10 --test-mode >/dev/null 2>&1; then
    # Check if container was created with correct name
    if docker ps -a --format "{{.Names}}" | grep -q "^ethusdt$"; then
        echo "✅ ethusdt input creates ethusdt container"

        # Wait a moment for container to start and generate logs
        sleep 2

        # Check the bot.sh output which shows the normalized symbol
        echo "Checking bot.sh output for symbol normalization..."
        if ./bot.sh start ethusdt --amount 10 --test-mode 2>&1 | grep -q "ETH/USDT:USDT"; then
            echo "✅ Symbol correctly normalized to ETH/USDT:USDT"
        else
            echo "⚠️ Symbol normalization not visible in bot.sh output (but functions work correctly)"
        fi
    else
        echo "❌ ethusdt container not created"
        all_passed=false
    fi
else
    echo "❌ Failed to start ethusdt bot"
    all_passed=false
fi

# Clean up test container
docker rm -f ethusdt 2>/dev/null || true

echo ""
echo "======================"
if [[ "$all_passed" == "true" ]]; then
    echo "🎉 All bug fixes verified successfully!"
    echo ""
    echo "✅ Symbol Normalization Fixed:"
    echo "   - ETHUSDT → ETH/USDT:USDT (not ETHUSDT/USDT:USDT)"
    echo "   - Container names no longer have duplicate 'usdt'"
    echo ""
    echo "✅ Return Code Handling Fixed:"
    echo "   - Successful commands return 0"
    echo "   - Failed commands return non-zero"
    echo "   - No more 'process.returncode or X' issues"
    echo ""
    echo "✅ Integration Tests Passed:"
    echo "   - End-to-end symbol processing works correctly"
    echo "   - Docker containers created with proper names"
    exit 0
else
    echo "❌ Some bug fixes failed verification"
    echo "Please check the issues above and fix them"
    exit 1
fi
