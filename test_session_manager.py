#!/usr/bin/env python3
"""Test session manager functionality"""

import sys
import os

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.infrastructure.telegram.telegram_base import UserSessionManager

def test_session_manager():
    """Test session manager functionality"""
    print("🧪 Testing UserSessionManager...")
    
    # Create session manager
    session_manager = UserSessionManager()
    
    user_id = 12345
    
    # Test initial state
    print(f"Initial wizard state: {session_manager.is_in_wizard(user_id)}")
    print(f"Initial wizard step: {session_manager.get_wizard_step(user_id)}")
    
    # Set wizard step
    session_manager.set_wizard_step(user_id, 'createbot_symbol')
    print(f"After setting step: {session_manager.is_in_wizard(user_id)}")
    print(f"Current step: {session_manager.get_wizard_step(user_id)}")
    
    # Set session data
    session_manager.set_session_data(user_id, 'createbot_profile', 'main')
    print(f"Profile data: {session_manager.get_session_data(user_id, 'createbot_profile')}")
    
    # Clear wizard
    session_manager.clear_wizard(user_id)
    print(f"After clearing: {session_manager.is_in_wizard(user_id)}")
    print(f"Step after clearing: {session_manager.get_wizard_step(user_id)}")
    
    print("✅ Session manager test completed")

if __name__ == "__main__":
    test_session_manager()
