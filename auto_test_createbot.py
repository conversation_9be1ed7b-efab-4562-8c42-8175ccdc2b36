#!/usr/bin/env python3
"""Auto test createbot command"""

import sys
import os
import asyncio
import subprocess
import time

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.infrastructure.telegram.telegram_api_client import TelegramAPIClient

async def auto_test_createbot():
    """Automatically test createbot command"""
    # Get credentials from environment variables
    token = os.getenv('TELEGRAM_BOT_TOKEN')
    chat_id = os.getenv('TELEGRAM_CHAT_ID')

    if not token or not chat_id:
        print("❌ Error: TELEGRAM_BOT_TOKEN and TELEGRAM_CHAT_ID environment variables are required")
        print("💡 Set them with:")
        print("   export TELEGRAM_BOT_TOKEN='your_bot_token'")
        print("   export TELEGRAM_CHAT_ID='your_chat_id'")
        print("💡 Or create a .env file (see .env.example)")
        return

    chat_id = int(chat_id)
    
    print("🤖 Auto testing /createbot command...")
    
    try:
        client = TelegramAPIClient(token)
        
        # Step 1: Send /createbot command
        print("📤 Step 1: Sending /createbot command...")
        await client.send_message(chat_id, "/createbot")
        
        # Wait a bit for bot to process
        await asyncio.sleep(3)
        
        # Step 2: Send symbol "eth"
        print("📤 Step 2: Sending symbol 'eth'...")
        await client.send_message(chat_id, "eth")
        
        # Wait a bit for bot to process
        await asyncio.sleep(3)
        
        # Step 3: Check Docker logs
        print("📋 Step 3: Checking Docker logs...")
        result = subprocess.run(
            ['docker', 'logs', 'telegram-bot', '--tail=50'],
            capture_output=True,
            text=True
        )
        
        print("🔍 Docker logs:")
        print("=" * 50)
        print(result.stdout)
        if result.stderr:
            print("STDERR:")
            print(result.stderr)
        print("=" * 50)
        
        # Step 4: Send amount "100"
        print("📤 Step 4: Sending amount '100'...")
        await client.send_message(chat_id, "100")
        
        await asyncio.sleep(2)
        
        print("✅ Auto test completed!")
        
    except Exception as e:
        print(f"❌ Auto test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(auto_test_createbot())
